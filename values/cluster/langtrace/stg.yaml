# Langtrace configuration for STAGING environment
langtrace:
  langtraceApp:
    name: langtrace
    image: "scale3labs/langtrace-client"
    langtrace_release: "latest"
    replicaCount: 1
    
  postgres:
    enabled: true
    storageSize: 50Gi
    storageClassName: "gcp-ssd"
    
  clickhouse:
    enabled: true
    storageSize: 50Gi
    storageClassName: "gcp-ssd"
    
  env:
    # Application Variables
    NEXT_PUBLIC_APP_NAME: "Langtrace - Staging"
    NEXT_PUBLIC_ENVIRONMENT: "staging"
    
    # Database credentials - should be moved to secrets
    postgres_user: "ltuser"
    postgres_password: "ltpasswd-stg"
    postgres_database: "langtrace"
    
    # Clickhouse credentials - should be moved to secrets
    CLICK_HOUSE_USER: "lt_clickhouse_user"
    CLICK_HOUSE_PASSWORD: "clickhousepw-stg"
    CLICK_HOUSE_DATABASE_NAME: "langtrace_traces"
    
    # Admin credentials - should be moved to secrets
    ADMIN_EMAIL: "<EMAIL>"
    ADMIN_PASSWORD: "admin-password-change-me"
    NEXT_PUBLIC_ENABLE_ADMIN_LOGIN: "true"
    
    # Azure AD Variables (configure as needed)
    AZURE_AD_CLIENT_ID: ""
    AZURE_AD_CLIENT_SECRET: ""
    AZURE_AD_TENANT_ID: ""

langtrace-common:
  enabled: true

istio-ingress:
  enabled: true
  istio:
    ingress:
      langtrace:
        enabled: true
        gateways:
          - common
        hosts:
          - "langtrace.gcp-stg.inspectorio.com"
        http:
          - route:
              - destination:
                  host: langtrace-svc.default.svc.cluster.local
                  port:
                    number: 3000
