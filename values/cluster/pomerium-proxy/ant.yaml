white_list: &white_list
  - 10.0.0.0/8
  - 34.234.152.119/32 #velocity
  - 35.173.143.17/32 #velocity
  - 34.237.19.8/32 #velocity
  - 86.57.217.184/32 #minsk office
  - 208.52.166.154/32 # bitrise xcode
  - 208.52.166.128/28 # bitrise xcode
  - 207.254.0.248/29 # bitrise xcode
  - 207.254.0.208/28 # bitrise xcode
  - 207.254.34.148/32 # bitrise xcode
  - 207.254.33.176/28 # bitrise xcode
  - 104.197.15.74/32 # bitrise linux
  - 34.125.50.224/32 # bitrise linux
  - 34.125.82.130/32 # bitrise linux
  - 35.202.121.43/32 # bitrise linux
  - 35.237.165.17/32 # bitrise linux
  - 35.231.56.118/32 # bitrise linux
  - *************/32 # (prod)
  - ***************/32 # (pre)
  - *************/32 # (pre)
  - *************/32 # (stg)
  - ************/32 # (stg)
  - *************/32 # (ant)
  - 3.26.128.128/26 # atlassian cloud
  - 3.69.198.0/26 # atlassian cloud
  - 3.101.177.128/26 # atlassian cloud
  - 3.251.213.64/26 # atlassian cloud
  - 13.52.5.0/25 # atlassian cloud
  - 13.52.5.96/28 # atlassian cloud
  - 13.200.41.128/25 # atlassian cloud
  - 13.200.41.224/28 # atlassian cloud
  - 13.214.1.0/26 # atlassian cloud
  - 13.236.8.128/25 # atlassian cloud
  - 13.236.8.224/28 # atlassian cloud
  - 16.63.53.128/25 # atlassian cloud
  - 16.63.53.224/28 # atlassian cloud
  - 18.136.214.0/25 # atlassian cloud
  - 18.136.214.96/28 # atlassian cloud
  - 18.184.99.128/25 # atlassian cloud
  - 18.184.99.224/28 # atlassian cloud
  - 18.234.32.128/25 # atlassian cloud
  - 18.234.32.224/28 # atlassian cloud
  - 18.246.31.128/25 # atlassian cloud
  - 18.246.31.224/28 # atlassian cloud
  - 18.246.188.0/25 # atlassian cloud
  - 18.246.188.32/28 # atlassian cloud
  - 35.84.197.128/26 # atlassian cloud
  - 43.202.69.0/25 # atlassian cloud
  - 43.202.69.96/28 # atlassian cloud
  - 44.197.146.192/26 # atlassian cloud
  - 44.220.40.128/25 # atlassian cloud
  - 44.220.40.160/28 # atlassian cloud
  - 52.215.192.128/25 # atlassian cloud
  - 52.215.192.224/28 # atlassian cloud
  - 104.192.136.0/21 # atlassian cloud
  - 104.192.136.0/24 # atlassian cloud
  - 104.192.136.240/28 # atlassian cloud
  - 104.192.137.0/24 # atlassian cloud
  - 104.192.137.240/28 # atlassian cloud
  - 104.192.138.0/24 # atlassian cloud
  - 104.192.138.240/28 # atlassian cloud
  - 104.192.140.0/24 # atlassian cloud
  - 104.192.140.240/28 # atlassian cloud
  - 104.192.142.0/24 # atlassian cloud
  - 104.192.142.240/28 # atlassian cloud
  - 104.192.143.0/24 # atlassian cloud
  - 104.192.143.240/28 # atlassian cloud
  - 185.166.140.0/22 # atlassian cloud
  - 185.166.140.0/24 # atlassian cloud
  - 185.166.140.112/28 # atlassian cloud
  - 185.166.141.0/24 # atlassian cloud
  - 185.166.141.112/28 # atlassian cloud
  - 185.166.142.0/24 # atlassian cloud
  - 185.166.142.240/28 # atlassian cloud
  - 185.166.143.0/24 # atlassian cloud
  - 185.166.143.240/28 # atlassian cloud
  - 3.81.253.49/32 # JellyFish us-east-1a
  - 35.174.89.88/32 # JellyFish us-east-1b
  - 172.188.72.227/32 # - Asia Pritunl VPN server (location - Southeast Asia)
  - 20.218.156.149/32 #- Europe Pritunl VPN server (location - Germany West Central)
  - 20.120.208.36/32 #- US Pritunl VPN server (location - West US)
  - 20.205.160.4/32 #- Chinese Pritunl VPN server (location - Southeast Asia)
  - 20.197.79.221/32 #- Chinese Pritunl VPN server (location - Southeast Asia)
  - 34.85.117.57/32 # sourcegraph demo
  - 34.146.198.136/32 # sourcegraph demo
  - 101.99.36.139/32 # China cisco VPN
  - 113.161.87.132/32 # China cisco VPN
  - 210.245.118.70/32 # China VPN new
  - 34.74.90.64/28 # gitlab.com
  - 34.74.226.0/24 # gitlab.com
  - 104.30.162.232/32 # Cloudflare egress singapore
  - 104.30.176.185/32 # Cloudflare egress frankfurt
  - **************/32 # Cloudflare egress hong kong
  - **************/32 # CodeRabbit
  - **************/32 # CodeRabbit

white_list_infra_egress: &white_list_infra_egress
  - *************/32 # (prod)
  - ***************/32 # (pre)
  - *************/32 # (pre)
  - *************/32 # (stg)
  - ************/32 # (stg)
  - *************/32 # (ant)
  - 10.0.0.0/14 # ant cluster pod range

pomerium-proxy:
  pomeriumService: http://pomerium-proxy.pomerium.svc.cluster.local
  clientIPExtractRegexp: "(?<IP>([0-9]{1,3}\\.){3}[0-9]{1,3}), (([0-9]{1,3}\\.){3}[0-9]{1,3})$"

  ingress:
    enabled: true # use Istio Ingress instead of
    annotations:
      kubernetes.io/ingress.class: nginx
      nginx.ingress.kubernetes.io/proxy-body-size: 512m
      nginx.ingress.kubernetes.io/proxy-connect-timeout: "15"
      nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
      nginx.ingress.kubernetes.io/service-upstream: "true"

  vhosts:
    gitlab.inspectorio.com:
      enabled: true
      direct_destination: http://gitlab-webservice-default.default.svc.cluster.local:8181
      pomerium_host_from: gitlab.inspectorio.com
      white_list: *white_list
      white_list_infra_egress: *white_list_infra_egress
      pathType: ImplementationSpecific
      forward_auth_url: http://gitlab-webservice-default.default.svc.cluster.local:8181/api/v4/version
      forward_auth_original_header: private_token #lowercase!
      gitlabRunnerConfigEnable: true
      direct_paths:
        - "~ ^/api/v4/projects/([0-9]+)/packages/npm"
        - "~ ^/-/jira_connect/oauth_application_id"
      paths:
        - /
      tls:
        - hosts:
            - gitlab.inspectorio.com
          secretName: inspectorio-com-common-tls

  nginx:
    replicaCount: 2
    service:
      type: ClusterIP
      ports:
        http: 80
        https: 443
      targetPort:
        http: 8080
        https: 8443
    existingServerBlockConfigmap: pomerium-proxy-servers-configmap
    containerPorts:
      http: 8080
      https: 8443

istio-ingress:
  istio:
    ingress:
      gitlab:
        enabled: True
        gateways:
          - service
        hosts:
          - "gitlab.inspectorio.com"
        http:
        - route:
          - destination:
              host: pomerium-proxy-nginx.pomerium.svc.cluster.local
              port:
                number: 80
          retries:
            attempts: 5
            perTryTimeout: 10s
            retryOn: gateway-error,connect-failure,refused-stream,reset,5xx
        tcp:
          - match:
              - port: 22
            route:
              - destination:
                  host: gitlab-gitlab-shell.default.svc.cluster.local
                  port:
                    number: 22
