mobileresponder:
  appservices:
    main:
      replicaCount: 3
      containers:
        main:
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
            limits:
              cpu: 2
              memory: 2Gi
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload='mobileresponder-appservice-main'}) / sum(kube_deployment_status_replicas{deployment='mobileresponder-appservice-main'})"
          interval: 5s
          name: mobileresponder_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 15
        scalers:
          main:
            enabled: true
            minReplicaCount: 3
            maxReplicaCount: 10
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 75
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 180
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 150
                    selectPolicy: Min
            triggers:
              cpu:
                value: "85"
                
  environment:
    AWS_DEFAULT_REGION: ap-northeast-1
    ASSIGNMENT_URL: http://sight-be-mobileresponder.sight-be.svc.cluster.local/inspectorio/ams
    SMS_URL: http://auth-router.default.svc.cluster.local/sms
    AQL_URL: http://sight-be-mobileresponder.sight-be.svc.cluster.local/inspectorio/aql
    INSPECTION_URL: http://sight-be-mobileresponder.sight-be.svc.cluster.local/inspectorio
    FMS_URL: http://fms-main.fms.svc.cluster.local/files-management
    ENV_URL: http://app.inspectorio.com
    BOUNCERS_URL: http://bouncers-main.bouncers.svc.cluster.local
    mode: production
    NMS_URL: http://notimanager-main.notimanager.svc.cluster.local/notimanager
    INSPECTIONS_BASE_URL: http://sight-be-mobileresponder.sight-be.svc.cluster.local/inspectorio/ims/v1
    NEW_RELIC_APP_NAME: saasprod_mobileresponder
    QUALITY_CONTROL_BE_URL: http://sight-be-mobileresponder.sight-be.svc.cluster.local
    BOOKING_BASE_URL: http://sight-be-mobileresponder.sight-be.svc.cluster.local/inspectorio/bms/v1
    MAX_ALLOWED_FILE_SIZE: "400"
    MASTER_DATA_BASE_URL: http://master-data-main.master-data.svc.cluster.local
    UWSGI_PROCESSES: 15
    SIGHT_ANDROID_REPORT_CHANNEL: "sight_android_incident_report"
    SIGHT_IOS_REPORT_CHANNEL: "sight_ios_incident_report"

  externalSecretStore:
    mobileresponder: # name of k8s secret
      source:
        data:
          - secretKey: SECRET_KEY # name of key in k8s secret
            remoteRef:
              key: mobileresponder-secret_key
          - secretKey: SENTRY_DSN
            remoteRef:
              key: mobileresponder-sentry-default
          - secretKey: REPORT_PROBLEM_SLACK_TOKEN
            remoteRef:
              key: mobileresponder-report-problem-slack-token

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress: {}
    egress:
      apps: # <namespace>:<app-name>
        - "bouncers:bouncers"
        - "fms:fms"
        - "master-data:master-data"
        - "notimanager:notimanager"
        - "sight-be:sight-be"

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  probes:
    http:
      mobileresponder-health-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - http://mobileresponder-main.mobileresponder.svc.cluster.local/health_check
            labels:
              class: saas-outage
              group: mobileresponder
              domain_type: private
              domain_site: private

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    default:
      enabled: true
      appservice: main
      ingressClass: api
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
        strip_path: true
        regex_priority: 100
      paths:
        - "/~/inspectorio/mobile-responses/(.*)"
      rewrite:
        uri: "/$(uri_captures[1])"

    bot-detection-v1:
      enabled: true
      appservice: main
      ingressClass: api
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/(?P<app_version>(4\.(2[3-9]|[3-9]\d)(\.\d{1,})?|[5-9](\.\d{1,}){1,2}|\d{2,}(\.\d{1,}){1,2}))'
            deny:
              - ".*"
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v1/(?<organization_id>[^/]+)/gateways/(sub-orgs)$"
        - "/~/inspectorio/mobile-responses/v1/(?<organization_id>[^/]+)/gateways/(factories)$"
        - "/~/inspectorio/mobile-responses/v1/(?<organization_id>[^/]+)/gateways/(suppliers)$"
        - "/~/inspectorio/mobile-responses/v1/(?<organization_id>[^/]+)/gateways/(brand-retailers)$"
        - "/~/inspectorio/mobile-responses/v1/(?<organization_id>[^/]+)/gateways/(put_assignment)$"
        - "/~/inspectorio/mobile-responses/v1/(?<organization_id>[^/]+)/gateways/(associated_orgs)$"
      rewrite:
        uri: "/v1/$(uri_captures['organization_id'])/gateways/$(uri_captures[2])"
    
    dss:
      enabled: true
      appservice: main
      ingressClass: api
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/(?P<app_version>(4\.(2[3-9]|[3-9]\d)(\.\d{1,})?|[5-9](\.\d{1,}){1,2}|\d{2,}(\.\d{1,}){1,2}))'
            deny:
              - ".*"
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v1/(?<organization_id>[^/]+)/gateways/dss/(?<target_org_id>[^/]+)/items(?<extra_path>.*)"
      rewrite:
        uri: "/v1/$(uri_captures['organization_id'])/gateways/dss/$(uri_captures['target_org_id'])/items$(uri_captures['extra_path'])"

    ics:
      enabled: true
      appservice: main
      ingressClass: api
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/(?P<app_version>(4\.(2[3-9]|[3-9]\d)(\.\d{1,})?|[5-9](\.\d{1,}){1,2}|\d{2,}(\.\d{1,}){1,2}))'
            deny:
              - ".*"
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v1/(?<organization_id>[^/]+)/gateways/ics/(?<inspection_uuid>[^/]+)/fabric-defects"
      rewrite:
        uri: "/v1/$(uri_captures['organization_id'])/gateways/ics/$(uri_captures['inspection_uuid'])/fabric-defects"

    v2-user-profile:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      route:
        methods:
          - POST
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v2/(user-profile$)"
      rewrite:
        uri: "/v2/gateways/$(uri_captures[1])"

    v2-login:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      route:
        methods:
          - POST
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v2/(login$)"
      rewrite:
        uri: "/v2/gateways/$(uri_captures[1])"

    v2-refresh-session:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      route:
        methods:
          - POST
          - GET
          - DELETE
          - PUT
          - PATCH
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v2/gateways/(refresh-session$)"
      rewrite:
        uri: "/v2/gateways/$(uri_captures[1])"

    v2-change-password:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      route:
        methods:
          - POST
          - GET
          - DELETE
          - PUT
          - PATCH
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v2/gateways/(change-password$)"
      rewrite:
        uri: "/v2/gateways/$(uri_captures[1])"
  
    login:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      route:
        methods:
          - POST
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v1/(login$)"
      rewrite:
        uri: "/v1/gateways/$(uri_captures[1])"

    userProfile:
      enabled: true
      appservice: main
      ingressClass: api
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v1/(userProfile$)"
      rewrite:
        uri: "/v1/gateways/$(uri_captures[1])"

    changePassword:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      route:
        methods:
          - POST
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v1/gateways/(changePassword$)"
      rewrite:
        uri: "/v1/gateways/$(uri_captures[1])"

    forgotPassword:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v1/gateways/(forgotPassword$)"
      rewrite:
        uri: "/v1/gateways/$(uri_captures[1])"

    resetPassword:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v1/gateways/(resetPassword$)"
      rewrite:
        uri: "/v1/gateways/$(uri_captures[1])"

    session:
      enabled: true
      appservice: main
      ingressClass: api
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      plugins: ["bouncer-auth-disabled"]
      route:
        methods:
          - POST
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v1/(session$)"
      rewrite:
        uri: "/v1/gateways/$(uri_captures[1])"

    refresh-session:
      enabled: true
      appservice: main
      ingressClass: api
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      plugins: ["bouncer-auth-disabled"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/mobile-responses/v1/gateways/(refresh/session$)"
      rewrite:
        uri: "/v1/gateways/$(uri_captures[1])"

    allow-cors:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        bot-detection:
          enabled: true
          plugin: bot-detection
          config:
            allow:
              - 'Inspectorio\s?((Sight|QRM)?|(-\sStaging)?|(-\sPreprod)?)\/((\d+\.)?(\d+\.)?(\*|\d+))'
            deny:
              - ".*"
      route:
        methods:
          - OPTIONS
        strip_path: true
      paths:
        - "/inspectorio/mobile-responses"
