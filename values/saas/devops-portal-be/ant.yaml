devops-portal-be:
  istio:
    ingress:
      default:
        enabled: true
        hosts:
          - devops-portal.inspectorio.com
        http:
          - timeout: 20s
            match:
              - uri:
                  prefix: /
                ignoreUriCase: true
            route:
              - destination:
                  host: devops-portal-be-main
                  port:
                    number: 80
            corsPolicy:
              allowOrigins:
                - exact: "*"
              allowMethods:
                - GET
                - HEAD
                - PUT
                - PATCH
                - POST
                - DELETE
                - OPTIONS
              allowHeaders:
                - User-Agent
                - X-Requested-With
                - If-Modified-Since
                - Cache-Control
                - Content-Type
                - Range
                - auth
                - authorization
                - Saas-Backend
                - x-client
                - rs-auth
                - Pragma
                - service
              exposeHeaders:
                - Cache-Control
                - Content-Language
                - Content-Type
                - Expires
                - Last-Modified
                - Pragma
                - location
              allowCredentials: false
              maxAge: "24h"
  
  environment:
    REDIS_HOST: "redis-redis-infra.external-service.svc.cluster.local"
    REDIS_DB: 3

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress: {}
    egress:
      apps: []
      custom:
      - ports:
        - port: 5432
          protocol: TCP
        - port: 6379
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
