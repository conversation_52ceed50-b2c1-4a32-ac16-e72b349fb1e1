imageproxy:
  appservices:
    rise:
      containers:
        main:
          resources:
            limits:
              cpu: 4
              memory: 4096Mi
            requests:
              cpu: 0.5
              memory: 1500Mi

  environment:
    IMAGEPROXY_ALLOWHOSTS: "rise-files.inspectorio.com"

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: true
      internal:
        rules:
          high-5xx-rate:
            enabled: true
    ingress:
      rise:
        enabled: True
        hosts:
          - "images.inspectorio.com"
        http:
          - match:
              - uri:
                  prefix: /rise/
                ignoreUriCase: true
            rewrite:
              uri: /
            route:
              - destination:
                  appservice: rise
            retries:
              attempts: 10
              perTryTimeout: 10s
              retryOn: gateway-error,connect-failure,refused-stream,reset,5xx

  probes:
    http:
      image-proxy-public-global-uptime-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - https://images.inspectorio.com/rise/
            labels:
              class: saas-outage
              group: imageproxy
              domain_type: public
              domain_site: global
  
  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps: []
      custom: []
    ingress:
      apps: []
