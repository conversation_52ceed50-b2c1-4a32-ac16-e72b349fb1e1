rs-backend:
  appservices:
    main:
      replicaCount: 8
      enabled: true
      containers:
        main: #container name within a pod
          resources:
            limits:
              cpu: 2
              memory: 3Gi
            requests:
              cpu: 0.2
              memory: 1Gi
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload=~'^.*rs-backend-appservice-main.*$'}) / sum(kube_deployment_status_replicas{deployment='rs-backend-appservice-main'})"
          interval: 15s # call expr each internval
          name: rs_backend_per_pod_rps # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 15
        scalers:
          main:
            enabled: true
            minReplicaCount: 8
            maxReplicaCount: 16
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 50
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 90
                    selectPolicy: Max
    bull-exporter:
      replicaCount: 1
      enabled: true
      service:
        type: ClusterIP
        port: 80
        targetPort: main
        enabled: true
        protocol: TCP
      monitoring:
        serviceMonitor:
          enabled: true
          endpoints:
            - port: bull-exporter
              path: /metrics
              interval: 60s
      containers:
        main:
          image:
            repository: uphabit/bull_exporter
            tag: latest
            pullPolicy: IfNotPresent
          podSecurityContext:
            runAsGroup: 65534
            runAsUser: 65534
            runAsNonRoot: true
            privileged: false
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - all
          resources:
            requests:
              cpu: 100m
              memory: 64M
            limits:
              cpu: 200m
              memory: 128M
          ports:
            - name: main
              containerPort: 9538
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /healthz
              port: main
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /healthz
              port: main
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 5
    bull-queue:
      replicaCount: 1
      enabled: true
      service:
        type: ClusterIP
        port: 80
        targetPort: main
        enabled: true
        protocol: TCP
      containers:
        main:
          environmentFromSecretStore:
            - app
          ports:
            - name: main
              containerPort: 3000
              protocol: TCP
          resources:
            limits:
              cpu: 2
              memory: 2Gi
            requests:
              cpu: 100m
              memory: 128Mi
          livenessProbe:
            httpGet:
              path: /health_check
              port: 3000
            initialDelaySeconds: 15
            periodSeconds: 20
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /health_check
              port: 3000
            initialDelaySeconds: 15
            periodSeconds: 20
            timeoutSeconds: 10

  environment:
    REQUEST_SIZE_LIMIT: "2mb"
    REDIS_URI: "redis://redis-redis-rs-production.external-service.svc.cluster.local:6379/0"
    EXPORTER_REDIS_URL: "redis://redis-redis-rs-production.external-service.svc.cluster.local:6379/0"
    EXPORTER_PREFIX: RISE
    EXPORTER_QUEUES: GENERATE_PDF PROCESS_EXTERNAL_REPORT CASE_EXPIRATION ASSESSMENT_GENERATOR
    POSTGRES_HOST: "pg-rs-backend-main.external-service.svc.cluster.local"
    POSTGRES_USER: "rs-backend"
    POSTGRES_DB: "rs-backend"
    PORT: "3000"
    DOCUSIGN_ENV: "na3"
    DOCUSIGN_ACCOUNT_ID: "6e778ef9-b108-4013-a062-1f849ce2bb56"
    DOCUSIGN_OAUTH_BASE_URL: "account.docusign.com"
    DOCUSIGN_PATH_PRIVATE_KEY: "/app/private-key/docusign_private.txt"
    APP_ENDPOINT: "https://rise.inspectorio.com"
    POSTGRES_PORT: "5432"
    SMS_BASE_URL: "http://auth-router.default.svc.cluster.local/sms/api/sms"
    SMS_SYS_ADMIN_EMAIL: "<EMAIL>"
    MAX_FILE_UPLOAD_SIZE: "********"
    DOCUSIGN_URL: "https://na3.docusign.net/restapi"
    DEDUPE_URL: "http://dedupe-api-main.dedupe-api.svc.cluster.local"
    EMAIL_ENDPOINT: "https://api.sendgrid.com/v3"
    EMAIL_SENDER: "<EMAIL>"
    EMAIL_SENDERNAME: "Inspectorio Rise"
    EMAIL_TEMPLATES_ASSESSMENT: "d-c856f952d3ff48e7ba5c537e7e977905"
    EMAIL_TEMPLATES_USER_INVITATION: "d-c21cdf15603d42cfaef1915797e402ff"
    EMAIL_TEMPLATES_USER_INVITATION_REMINDER: "d-3d1318de333b4259ae175e77b6a75a4b"
    EMAIL_TEMPLATES_USER_SIGNING_REMINDER: "d-f9f5cd69a2774dd3893169812ad918d6"
    EMAIL_TEMPLATES_PENDING_ASSESSMENTS_REMINDER: "d-00ad1c9c10474ea4a07e5719bc09aef2"
    EMAIL_TEMPLATES_DUE_DATE_REMINDER: "d-1ddf14a590104d78aa48995ce85bfe8b"
    GEODB_BASE_URL: "https://wft-geo-db.p.rapidapi.com/v1/geo"
    DOCUSIGN_SUBSCRIPTION_DURATION: "12"
    DOCUSIGN_TEMPLATE_PILOT_ID: "5819f182-ef1f-4ca7-8fbd-49d48a337ec6"
    DOCUSIGN_PILOT_DURATION: "12"
    ORIGIN_FOR_SMS_LOGIN: "https://rise.inspectorio.com"
    ONE_ORG_ENABLE: "false"
    EMAIL_TEMPLATES_ORG_SIGNING_REMINDER: "d-950736db6d214d13bd45fa1775388384"
    SLCP_VERSION: "v1.3"
    SLCP_BASE_URL: "https://gateway.slconvergence.org/api/slcp"
    HIGG_REPORTS_ASSESSMENT_ID: "472aef5d-6bd3-4e02-88cb-24978731a61d"
    SLCP_REPORTS_ASSESSMENT_ID: "56ee48cb-66d3-47bc-9f9a-e6ba2b81046a"
    EMAIL_TEMPLATES_CAPA_NOTIFICATION: "d-933aa33757ca49058fa455525490a246"
    EMAIL_TEMPLATES_CASE_STATUS: "d-24bff4722c874740983d1f9ce0d13de8"
    EMAIL_TEMPLATES_NON_COMPLIANT: "d-e9c123171fbe4ed3b8f83ee1f1650430"
    EMAIL_TEMPLATES_ORGANIZATION_INVITATION: "d-87b696225a6f49328cd2152e19cbfc26"
    EMAIL_TEMPLATES_EDIT_ASSESSMENT_FOR_OLD_EXECUTOR: "d-c73a7d84fc88411c94ce116517707f4c"
    EMAIL_TEMPLATES_DISCLOSURE: "d-c67d5a2357f241e6aac0a3c303c5bc20"
    EMAIL_TEMPLATES_OFFLINE_SYNC_ISSUE: "d-569a9009ca304c7eb6bac5517dd14a79"
    FORCE_UPDATE_FOR_DEPLOYEMENT: "0"
    PRODUCT_CONTEXT: "rise"
    DOCEBO_ENABLE: "true"
    DOCEBO_URL: "https://inspectorio.docebosaas.com"
    EMAIL_TEMPLATES_SEND_ESCALATION: "d-bbe95faef8e1452dac286bc8342955cd"
    EMAIL_TEMPLATES_GENERATE_PDF_ISSUE: "d-0abc1242ee9745b7b4348b882550e583"
    TERMS_AND_CONDITIONS_ID_EN: "12782634"
    TERMS_AND_CONDITIONS_ID_ES: "41251907"
    TERMS_CAMPAIGN_NAMES: "Covid-19"
    NODE_OPTIONS: "--max_old_space_size=8192"
    ADMIN_EMAIL_LIST: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
    ONE_ORG_BASE_URL: ""
    QAFA_STANDARDS_WITH_NOTIFICATIONS: "cd5bdb6d-9037-41a3-b0b5-42bdd30a8423,bce078a0-e4a7-4605-8478-3d94c3c38e97"
    SSO_SERVICE_WHITE_LIST: "hermes"
    GCP_BUCKET_NAME: "rs-upload-prod"
    STORAGE_PROVIDER: "gcp"
    RISE_INTEGRATION_BASE_URL: "https://rise.inspectorio.com/api/v1"
    ENABLE_AKAMAI_EDGE_AUTH: "true"
    AKAMAI_BUCKET_URL: "https://rise-files.inspectorio.com"
    RISE_TRANSLATION_BASE_URL: "http://translation-main.translation.svc.cluster.local"
    HERMES_BASE_URL: "http://hermes-be-main.hermes.svc.cluster.local"
    MASTER_DATA_BASE_URL: "http://master-data-main.master-data.svc.cluster.local"
    MASTER_DATA_INTERNAL_ENDPOINT: "http://master-data-main.master-data.svc.cluster.local/internal-api/meta-data"
    EMAIL_TEMPLATES_ASSESSMENT_V2: "d-6ad52a2b99ff47329df9d41bd38092a7"
    EMAIL_TEMPLATES_BULK_ASSESSMENT_NOTIFICATION: "d-5b4b819d1ec141f8bf4b5bcdb802ae8b"
    EMAIL_TEMPLATES_CAPA_NOTIFICATION_V2: "d-62c91ab6f72742c7a6ec5f1d1ad08e97"
    POSTGRES_MIN_POOL: "5"
    POSTGRES_MAX_POOL: "20"
    ACQUIRE_TIMEOUT_MILLIS: "30000"
    REAP_INTERVAL_MILLIS: "2000"
    PASSPORT_BASE_URL: "http://passport-be-main.passport.svc.cluster.local"
    THIRDPARTY_REPORTS_HOST: "thirdparty-reports-main.thirdparty-reports.svc.cluster.local"
    LOG_LEVEL: "warn"
    SAC_ABBR_NAME: "SAC"
    SSO_BASE_URL: "https://id.inspectorio.com/v1/sso"
    DOCEBO_CLIENT_ID: "Inspectorio"
    DOCUMENT_VALIDATOR_BASE_URL: "http://document-validator-main.document-validator.svc.cluster.local"
    BULL_BOARD_BASE_PATH: /queues
    INTEGRATION_GATEWAY_ADMIN_URL: "http://kong-gateway-ingress-integration-cp-admin.kong.svc.cluster.local:8001"
    EMAIL_TEMPLATES_UPDATE_SCHEDULING: "d-dbd5376da7b84d4fbc041e454a3cfe35"
    ORG_RESOLVER_BASE_URL: "http://org-resolver-main.org-resolver.svc.cluster.local"
    SIGHT_BASE_URL: "http://sight-be-main.sight-be.svc.cluster.local"
    DATALAKE_BUCKET_NAME: "inspectorio-ds-datalake-prod"

  environmentFromSecrets:
    ES_DATASYNC_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: eck-datasync-pg-rs-backend-user
          key: password

  infra:
    services:
      kafkacluster:
        main:
          kafkaconnect:
            image: asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/kafka/sink-kafka-connect:1.0.0-dev-df60dbc0
            externalCluster: kafka-main-kafka-bootstrap.kafka.svc.cluster.local:9092 # optional. Address of already existing cluster.
            replicas: 1
            resources:
              requests:
                cpu: "300m"
                memory: 2Gi
              limits:
                cpu: 3
                memory: 2Gi
            jvmOptions:
              -Xmx: 1g
              -Xms: 1g
            externalConfiguration:
              env:
                - name: ES_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: eck-datasync-pg-connector-user
                      key: password
            readinessProbe:
              initialDelaySeconds: 60
              timeoutSeconds: 15
            livenessProbe:
              initialDelaySeconds: 60
              timeoutSeconds: 15
            metrics:
              enabled: True
            config:
              config.providers: env
              config.providers.env.class: org.apache.kafka.common.config.provider.EnvVarConfigProvider
              config.storage.replication.factor: 3
              offset.storage.replication.factor: 3
              status.storage.replication.factor: 3
            networkAccessFrom:
              - kafka-ui

            connectors:
              es.datasync-pg-organizations:
                type: sink
                class: io.confluent.connect.elasticsearch.ElasticsearchSinkConnector
                tasksMax: 3
                config:
                  topics: kafka-connect-cdc-main.pg.rs-backend.public.organizations
                  connection.url: http://eck-datasync-pg-es-http.eck-datasync.svc:9200
                  connection.username: connector
                  connection.password: ${env:ES_PASSWORD}
                  key.ignore: false
                  schema.ignore: true
                  write.method: insert
                  behavior.on.null.values: delete
                  flush.synchronously: true
                  transforms: route,extractKey,ReplaceField
                  transforms.route.regex: kafka-connect-cdc-main.pg.(.*)
                  transforms.route.replacement: $1
                  transforms.route.type: org.apache.kafka.connect.transforms.RegexRouter
                  transforms.extractKey.type: org.apache.kafka.connect.transforms.ExtractField$Key
                  transforms.extractKey.field: id
                  transforms.ReplaceField.type: org.apache.kafka.connect.transforms.ReplaceField$Value
                  transforms.ReplaceField.exclude: >-
                    social_networks,
                    meta

  datadog:
    enabled: auto
    options:
      #https://docs.datadoghq.com/tracing/trace_collection/library_config/python/#pagetitle
      - name: DD_LOGS_INJECTION
        value: "false"
      - name: DD_TRACE_RATE_LIMIT
        value: 10
      - name: DD_TRACE_SAMPLE_RATE
        value: 1.0

  probes:
    http:
      rs-backend-private-health-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - http://rs-backend-main.rs.svc.cluster.local/health_check
            labels:
              class: saas-outage
              group: rs-backend
      rs-backend-public-global-uptime-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - https://rise-api.inspectorio.com/status
            labels:
              class: saas-outage
              group: rs-backend
              domain_type: public
              domain_site: global
      rs-backend-public-china-uptime-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - https://rise-api.inspectorio-platform.com/status
            labels:
              class: saas-outage
              group: rs-backend
              domain_type: public
              domain_site: china

  workers:
    default:
      enabled: true
      replicaCount: 1
      containers:
        main:
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 1
              memory: 1Gi
          environment:
            TGT_HIGG_USERNAME: "<EMAIL>"
            DG_HIGG_USERNAME: "<EMAIL>"
            ENABLE_PDF_QUEUE: "true"
            MAP_TO_ORGANIZATION_NAME: "Demo Retailer ABC"
            WORKER_ROLE: "worker"
            EMAIL_RECEIVERS: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
            EMAIL_TEMPLATES_REPORTS_UPLOADED: "d-dfe14110c5894027b764a96a5c394128"
            DOWNLOAD_IMPORT_SCHEDULE: "30 23,9 * * *"
            FACTORIES_NOTIFICATIONS_EMAIL: "<EMAIL>"
            USE_SAC_ID_AS_EXTERNAL: "false"
            SLCP_ABBR_NAME: "SLCP"
            SYSTEM_ADMIN_EMAIL: "<EMAIL>"
            FR_HIGG_USERNAME: "<EMAIL>"
            SEND_NOTIFICATIONS_TO_REAL_FACTORIES: "true"
            SKIP_ORGANIZATION_MAPPING: "true"
            START_IMPORT_ON_INIT: false
            CHECK_NOTIFICATIONS_SCHEDULE: ""
            SKIP_SLCP_IMPORT: "true"
            KAFKA_SUBSCRIPTION_SYNC_BROKER_ENDPOINT: "kafka-prd-headless.default.svc.cluster.local:9092"
            KAFKA_SUBSCRIPTION_SYNC_TOPIC: "subscription"

    pdf-generator:
      enabled: true
      containers:
        main:
          resources:
            limits:
              cpu: 2.5
              memory: 4Gi
            requests:
              cpu: 500m
              memory: 2Gi
          environment:
            TGT_HIGG_USERNAME: "<EMAIL>"
            DG_HIGG_USERNAME: "<EMAIL>"
            ENABLE_PDF_QUEUE: "true"
            SLCP_LINKED_STANDARD_ID: "5340c3a0-1efc-41c4-811e-6c0d49783e61"
            WORKER_ROLE: "worker_pdf_generator"
            MAP_TO_ORGANIZATION_NAME: "Demo Retailer ABC"
            EMAIL_RECEIVERS: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
            EMAIL_TEMPLATES_REPORTS_UPLOADED: "d-b4943b48fb8c4f3fa275235ec761b2da"
            DOWNLOAD_IMPORT_SCHEDULE: "30 23,9 * * *"
            SEND_NOTIFICATIONS_TO_REAL_FACTORIES: "true"
            FACTORIES_NOTIFICATIONS_EMAIL: "<EMAIL>"
            USE_SAC_ID_AS_EXTERNAL: "false"
            SLCP_ABBR_NAME: "SAC"
            SKIP_ORGANIZATION_MAPPING: "true"
            SYSTEM_ADMIN_EMAIL: "<EMAIL>"
            FR_HIGG_USERNAME: "<EMAIL>"
            START_IMPORT_ON_INIT: false
            SKIP_SLCP_IMPORT: "true"
            CHECK_NOTIFICATIONS_SCHEDULE: ""
            KAFKA_SUBSCRIPTION_SYNC_BROKER_ENDPOINT: "kafka-prd-headless.default.svc.cluster.local:9092"
            KAFKA_SUBSCRIPTION_SYNC_TOPIC: "subscription"
            JOB_TIMEOUT_MINUTES: "3"
          livenessProbe:
            exec:
              command:
                - bash
                - -c
                - HEALTHZ=true node build/src/server.js
            initialDelaySeconds: 10
            periodSeconds: 30
            timeoutSeconds: 20

    assessment-generator:
      enabled: true
      containers:
        main:
          resources:
            requests:
              memory: 256Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 1
          environment:
            WORKER_ROLE: "worker_assessment_generator"
            RSC_BACKEND_URL: http://rs-backend-main.rs.svc.cluster.local
          livenessProbe:
            exec:
              command:
                - bash
                - -c
                - HEALTHZ=true node build/src/server.js
            initialDelaySeconds: 10
            periodSeconds: 30
            timeoutSeconds: 20

    email-sender:
      enabled: true
      containers:
        main:
          resources:
            requests:
              memory: 256Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 1
          environment:
            WORKER_ROLE: "worker_email_sender"
            RSC_BACKEND_URL: http://rs-backend-main.rs.svc.cluster.local
          livenessProbe:
            exec:
              command:
                - bash
                - -c
                - HEALTHZ=true node build/src/server.js
            initialDelaySeconds: 10
            periodSeconds: 30
            timeoutSeconds: 20

    sync-asm-data:
      enabled: true
      replicaCount: 1
      containers:
        main:
          resources:
            limits:
              cpu: 1
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 128Mi
          environment:
            WORKER_ROLE: "sync_asm_data"

    case-expiration-processor:
      enabled: true
      replicaCount: 1
      containers:
        main:
          resources:
            limits:
              cpu: 1
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 128Mi
          environment:
            TGT_HIGG_USERNAME: "<EMAIL>"
            DG_HIGG_USERNAME: "<EMAIL>"
            WORKER_ROLE: "case_expiration_processor"
            ENABLE_PDF_QUEUE: "true"
            SKIP_SLCP_IMPORT: "ture"
            MAP_TO_ORGANIZATION_NAME: "Demo Retailer ABC"
            EMAIL_RECEIVERS: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
            EMAIL_TEMPLATES_REPORTS_UPLOADED: "d-dfe14110c5894027b764a96a5c394128"
            DOWNLOAD_IMPORT_SCHEDULE: "30 23,9 * * *"
            FACTORIES_NOTIFICATIONS_EMAIL: "<EMAIL>"
            USE_SAC_ID_AS_EXTERNAL: "false"
            SLCP_ABBR_NAME: "SLCP"
            SLCP_LINKED_STANDARD_ID: "5340c3a0-1efc-41c4-811e-6c0d49783e61"
            SYSTEM_ADMIN_EMAIL: "<EMAIL>"
            FR_HIGG_USERNAME: "<EMAIL>"
            SEND_NOTIFICATIONS_TO_REAL_FACTORIES: "true"
            SKIP_ORGANIZATION_MAPPING: "ture"
            START_IMPORT_ON_INIT: true
            CHECK_NOTIFICATIONS_SCHEDULE: ""
            KAFKA_SUBSCRIPTION_SYNC_BROKER_ENDPOINT: "kafka-prd-headless.default.svc.cluster.local:9092"
            KAFKA_SUBSCRIPTION_SYNC_TOPIC: "subscription"

    external-report-processor:
      enabled: true
      replicaCount: 1
      containers:
        main:
          resources:
            limits:
              cpu: 1
              memory: 2Gi
            requests:
              cpu: 100m
              memory: 256Mi
          environment:
            TGT_HIGG_USERNAME: "<EMAIL>"
            DG_HIGG_USERNAME: "<EMAIL>"
            WORKER_ROLE: "external_report_processor"
            SLCP_LINKED_STANDARD_ID: "5340c3a0-1efc-41c4-811e-6c0d49783e61"
            ENABLE_PDF_QUEUE: "true"
            SEND_NOTIFICATIONS_TO_REAL_FACTORIES: "true"
            MAP_TO_ORGANIZATION_NAME: "Demo Retailer ABC"
            EMAIL_RECEIVERS: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
            EMAIL_TEMPLATES_REPORTS_UPLOADED: "d-b4943b48fb8c4f3fa275235ec761b2da"
            DOWNLOAD_IMPORT_SCHEDULE: "30 23,9 * * *"
            FACTORIES_NOTIFICATIONS_EMAIL: "<EMAIL>"
            USE_SAC_ID_AS_EXTERNAL: "false"
            SKIP_ORGANIZATION_MAPPING: "true"
            SLCP_ABBR_NAME: "SLCP"
            SYSTEM_ADMIN_EMAIL: "<EMAIL>"
            FR_HIGG_USERNAME: "<EMAIL>"
            START_IMPORT_ON_INIT: false
            SKIP_SLCP_IMPORT: "true"
            CHECK_NOTIFICATIONS_SCHEDULE: ""
            KAFKA_SUBSCRIPTION_SYNC_BROKER_ENDPOINT: "kafka-prd-headless.default.svc.cluster.local:9092"
            KAFKA_SUBSCRIPTION_SYNC_TOPIC: "subscription"
            JOB_TIMEOUT_MINUTES: "3"
          livenessProbe:
            exec:
              command:
                - bash
                - -c
                - HEALTHZ=true node build/src/server.js
            initialDelaySeconds: 10
            periodSeconds: 30
            timeoutSeconds: 20

  externalSecretStore:
    job:
      generateSecretName: true
      source:
        data:
          - secretKey: PASSWORD
            remoteRef:
              key: rs-backend-sms-rs-backend-password

    eck-datasync-pg-connector-user:
      template:
        engineVersion: v2
        data:
          username: "{{ .username }}"
          password: "{{ .password }}"
          roles: "{{ .roles }}"
      source:
        dataFrom:
          - extract:
              key: eck-datasync-connector
              conversionStrategy: Default
              decodingStrategy: None

    eck-datasync-pg-rs-backend-user:
      template:
        engineVersion: v2
        data:
          username: "{{ .username }}"
          password: "{{ .password }}"
          roles: "{{ .roles }}"
      source:
        dataFrom:
          - extract:
              key: eck-datasync-rs-backend
              conversionStrategy: Default
              decodingStrategy: None

  cronjobs:
    office-mapping-synchronize:
      enabled: true
      concurrencyPolicy: Forbid
      schedule: "0 */12 * * *"
      shareProcessNamespace: True
      restartPolicy: Never
      containers:
        main:
          command:
            - node
            - ./build/scripts/sync_factory_mapping/sync_factory_mapping.js
          resources:
            limits:
              cpu: 4
              memory: 4Gi
            requests:
              cpu: 1
              memory: 1Gi
          environment:
            USER_NAME: "<EMAIL>"
          environmentFromSecretStore:
            - worker
            - app
            - job

  jobs:
    migration:
      enabled: true
      ttlSecondsAfterFinished: 60
      disabledGenerateName: true
      annotations:
        argocd.argoproj.io/hook: PreSync
        argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
        argocd.argoproj.io/sync-options: PrunePropagationPolicy=background
      istio:
        enabled: false
      parallelism: 1
      backoffLimit: 1
      completions: 1
      activeDeadlineSeconds: 3600
      restartPolicy: Never
      containers:
        main:
          environmentFromSecretStore:
            - worker
            - app
          ports: []
          resources:
            requests:
              memory: 256Mi
              cpu: 100m
            limits:
              memory: 2Gi
              cpu: 2
          command:
            - bash
            - -c
            - "npm run migrate:${ENV_NAME}"
          args: [""]

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: true
      internal:
        rules:
          high-5xx-rate:
            enabled: true
    ingress:
      default:
        enabled: True
        hosts:
          - "rise-api.inspectorio-platform.com"
          - "rise-api.inspectorio.com"
        ratelimit:
          login:
            enabled: true
            condition:
              headers:
                - name: "Path-NoParameters"
                  string_match:
                    exact: /login
                - name: "Traffic-class"
                  string_match:
                    exact: world
            threshold: 10rpm # this one need to match pre-defined threshold
          auth-login:
            enabled: true
            condition:
              headers:
                - name: "Path-NoParameters"
                  string_match:
                    exact: /auth/login
                - name: "Traffic-class"
                  string_match:
                    exact: world
            threshold: 10rpm # this one need to match pre-defined threshold
        http:
          - timeout: 60s
            match:
              - uri:
                  prefix: /
                ignoreUriCase: true
            route:
              - destination:
                  appservice: "main"
            headers:
              response:
                add:
                  cache-control: no-transform # Necessary to disable gzip compression for China users
        denyRules:
          deny:
            when:
              - key: request.headers[Path-Insensitive]
                values:
                  - "/data-management/add-subscriptions"
                  - "/data-management/add-subscriptions/"
                  - "/data-management/deactivate-subscriptions"
                  - "/data-management/deactivate-subscriptions/"
        customHttpRoutes:
          pre:
            - match:
                - headers:
                    traffic-class:
                      exact: inspectorio
                  ignoreUriCase: true
                  uri:
                    prefix: /swagger-ui/
                - headers:
                    traffic-class:
                      exact: inspectorio
                  ignoreUriCase: true
                  uri:
                    prefix: /swagger-json/
                - headers:
                    traffic-class:
                      exact: inspectorio
                  ignoreUriCase: true
                  uri:
                    prefix: /swagger.json
                - headers:
                    traffic-class:
                      exact: inspectorio
                  ignoreUriCase: true
                  uri:
                    prefix: /internal/graphql
                - headers:
                    traffic-class:
                      exact: inspectorio
                  ignoreUriCase: true
                  uri:
                    prefix: /api/internal
              route:
                - destination:
                    host: rs-backend-main
                    port:
                      number: 80
            - match:
                - ignoreUriCase: true
                  uri:
                    prefix: /swagger-ui/
                - ignoreUriCase: true
                  uri:
                    prefix: /swagger-json/
                - ignoreUriCase: true
                  uri:
                    prefix: /swagger.json
                - ignoreUriCase: true
                  uri:
                    prefix: /internal/graphql
                - ignoreUriCase: true
                  uri:
                    prefix: /api/internal
              directResponse:
                body:
                  string: >-
                    You need to use authorized network to access this service. Please
                    contact administrator !
                status: 403
  kongingress:
    int-v2: # platform vision: integration.stg.inspectorio.com
      enabled: true
      appservice: main
      ingressClass: integration
      customPlugins:
        auth:
          enabled: true
          plugin: apikey-auth
          config:
            proxy_header_names:
              - "session"
            product: "rise"
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
        regex_priority: 100
      rewrite:
        uri: /integration/v2/$(uri_captures[1])
      paths:
        - /~/rsc/v2/(.*)
    int-v2-legacy:
      enabled: true
      appservice: main
      ingressClass: integration
      customPlugins:
        auth:
          enabled: true
          plugin: apikey-auth
          config:
            proxy_header_names:
              - "session"
            product: "rise"
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - PATCH
          - GET
        strip_path: false
        regex_priority: 100
      rewrite:
        uri: /integration/v2/$(uri_captures[1])
      paths:
        - /~/api/v2/(.*)
      hosts:
        - domains:
            - rise-integration.inspectorio.com
            - rise.inspectorio.com
            - rise.inspectorio-platform.com
          tlsSecretName: inspectorio-com-common-tls
  alertrules:
    prometheusName: prometheus-main
    interval: 1m
    rules:
      SlowBullJobCompletionP90:
        runbook_url: https://grafana.inspectorio.com/d/d5e5fab1-5a1b-474a-b33d-4d827dd87a3d/bull-queue-prometheus?orgId=1&var-cluster_type=prd&var-prometheus=P8779A4CBE2E50ED2&var-container=rs-backend&from=now-7d&to=now
        summary: rs-backend's job queue {{ $labels.queue }} has 90th percentile more than 5 minutes - {{ $value }} seconds
        severity:
          warning:
            expr: sum(bull_queue_complete_duration{app_kubernetes_io_instance="rs-backend", quantile = "0.9"}) by (queue) / 1000 > 180 < 300
            for: 5m
          error:
            expr: sum(bull_queue_complete_duration{app_kubernetes_io_instance="rs-backend", quantile = "0.9"}) by (queue) / 1000 > 300
            for: 2m
      HighFailuresByQueue:
        runbook_url: https://grafana.inspectorio.com/d/d5e5fab1-5a1b-474a-b33d-4d827dd87a3d/bull-queue-prometheus?orgId=1&var-cluster_type=prd&var-prometheus=P8779A4CBE2E50ED2&var-container=rs-backend&from=now-7d&to=now
        summary: rs-backend's job queue {{ $labels.queue }} has more than 20 failures failures
        severity:
          warning:
            expr: max(bull_queue_failed{app_kubernetes_io_instance="rs-backend"}) by (queue) >= 20
            for: 5m

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps:
      - org-resolver:org-resolver
      - rs:rs-backend
      - dedupe-api:dedupe-api
      - sight-be:sight-be
      - hermes:hermes-be
      - document-validator:document-validator
      - master-data:master-data
      - passport:passport-be
      - kong:ingress-integration-cp
      - translation:translation
      - thirdparty-reports:thirdparty-reports
      custom:
      - ports:
        - port: 5432
          protocol: TCP
        - port: 6379
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
    ingress:
      apps:
      - sms:sms
      - superset:apache-superset
      - h1way:h1way
      - analytic3:analytic3
      - passport:passport-be
      - hermes:hermes-be
      - superset:analytics-superset
      - rs:rise-integration-api
      - document-validator:document-validator
