passport-be:
  appservices:
    main: # name of appservice. Name has been used for labels/matcheers/kongIngress configuration
      enabled: true
      replicaCount: 5
      containers:
        main: #container name within a pod
          ports:
            - name: main
              containerPort: 8000
              protocol: TCP
          resources:
            requests:
              memory: 1Gi
              cpu: 500m
            limits:
              memory: 4Gi
              cpu: 4
      kedaHPA:
        scalers:
          main:
            enabled: true
            minReplicaCount: 6
            maxReplicaCount: 12
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 75
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 20
                        periodSeconds: 90
                    selectPolicy: Max
            triggers:
              cpu:
                value: "80"
      monitoring:
        serviceMonitor:
          endpoints:
            - port: main #must be equal name of appservice
              path: metrics/
              interval: 60s

  workers:
    celery:
      containers:
        default:
          resources:
            requests:
              memory: 1344Mi
              cpu: 50m
            limits:
              memory: 8Gi
              cpu: 2
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{queue_name=~'celery', exported_namespace='passport-be'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{hostname=~'.*worker-celery.*', exported_namespace='passport-be'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-celery.*',le='+Inf', exported_namespace='passport-be'}[1m]))-sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-celery.*',le='2.5', exported_namespace='passport-be'}[1m])))or vector(1)))"
          interval: 10s # call expr each internval
          name: passport_celery_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
        scalers:
          worker:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 15

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps:
      - rs:rs-backend
      - sms:sms
      - master-data:master-data
      - notimanager:notimanager
      - integration-api:integration-api
      - eck-datasync:eck-datasync-pg
      - fms:fms
      - sight-be:sight-be
      custom:
      - ports:
        - port: 5432
          protocol: TCP
        - port: 6379
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
    ingress:
      apps:
      - rs:rs-backend
      - sms:sms
      - integration-api:integration-api
      - hermes:hermes-be
      - sight-be:sight-be

  infra:
    services:
      kafkacluster:
        main:
          kafkaconnect:
            image: asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/kafka/sink-kafka-connect:1.0.0-dev-df60dbc0
            externalCluster: kafka-main-kafka-bootstrap.kafka.svc.cluster.local:9092 # optional. Address of already existing cluster.
            replicas: 1
            resources:
              requests:
                cpu: "300m"
                memory: 4Gi
              limits:
                cpu: 3
                memory: 4Gi
            jvmOptions:
              -Xmx: 2g
              -Xms: 2g
            externalConfiguration:
              env:
                - name: ES_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: eck-datasync-pg-connector-user
                      key: password
                - name: PG_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: passport-be
                      key: PG_CONNECTOR_PASSWORD
            readinessProbe:
              initialDelaySeconds: 60
              timeoutSeconds: 15
            livenessProbe:
              initialDelaySeconds: 60
              timeoutSeconds: 15
            metrics:
              enabled: True
            config:
              config.providers: env
              config.providers.env.class: org.apache.kafka.common.config.provider.EnvVarConfigProvider
              config.storage.replication.factor: 3
              offset.storage.replication.factor: 3
              status.storage.replication.factor: 3
            networkAccessFrom:
              - kafka-ui

            connectors:
              es.datasync-pg-sm-organization:
                type: sink
                class: io.confluent.connect.elasticsearch.ElasticsearchSinkConnector
                tasksMax: 3
                config:
                  topics: kafka-connect-cdc-main.pg.passport-be.public.sm_organization
                  connection.url: http://eck-datasync-pg-es-http.eck-datasync.svc:9200
                  connection.username: connector
                  connection.password: ${env:ES_PASSWORD}
                  key.ignore: false
                  schema.ignore: true
                  write.method: insert
                  behavior.on.null.values: delete
                  flush.synchronously: true
                  transforms: route,extractKey,ReplaceField
                  transforms.route.regex: kafka-connect-cdc-main.pg.(.*)
                  transforms.route.replacement: $1
                  transforms.route.type: org.apache.kafka.connect.transforms.RegexRouter
                  transforms.extractKey.type: org.apache.kafka.connect.transforms.ExtractField$Key
                  transforms.extractKey.field: id
                  transforms.ReplaceField.type: org.apache.kafka.connect.transforms.ReplaceField$Value
                  transforms.ReplaceField.exclude: >-
                    rejected_reason,
                    phone,
                    city,
                    province,
                    country,
                    postal,
                    product_category,
                    no_of_worker,
                    no_of_supervisor,
                    no_of_manager,
                    no_of_ex_staff,
                    no_of_male_employee,
                    no_of_female_employee,
                    picture,
                    level,
                    latitude,
                    longitude,
                    is_internal,
                    technical_email,
                    stripe_customer_id,
                    csm_email,
                    renewal_type,
                    billing_information,
                    billing_email

              es.datasync-pg-sm-organization-generic:
                type: sink
                class: io.confluent.connect.elasticsearch.ElasticsearchSinkConnector
                tasksMax: 3
                config:
                  topics: kafka-connect-cdc-main.pg.passport-be.public.sm_organization_generic
                  connection.url: http://eck-datasync-pg-es-http.eck-datasync.svc:9200
                  connection.username: connector
                  connection.password: ${env:ES_PASSWORD}
                  key.ignore: false
                  schema.ignore: true
                  write.method: insert
                  behavior.on.null.values: delete
                  flush.synchronously: true
                  transforms: route,extractKey,ReplaceField
                  transforms.route.regex: kafka-connect-cdc-main.pg.(.*)
                  transforms.route.replacement: $1
                  transforms.route.type: org.apache.kafka.connect.transforms.RegexRouter
                  transforms.extractKey.type: org.apache.kafka.connect.transforms.ExtractField$Key
                  transforms.extractKey.field: id
                  transforms.ReplaceField.type: org.apache.kafka.connect.transforms.ReplaceField$Value
                  transforms.ReplaceField.exclude: >-
                    city,
                    province,
                    country,
                    postal,
                    stripe_customer_id,
                    csm_email,
                    billing_address

              es.datasync-pg-ecosystem-organization:
                type: sink
                class: io.confluent.connect.elasticsearch.ElasticsearchSinkConnector
                tasksMax: 3
                config:
                  topics: kafka-connect-cdc-main.pg.passport-be.public.ecosystem_organization
                  connection.url: http://eck-datasync-pg-es-http.eck-datasync.svc:9200
                  connection.username: connector
                  connection.password: ${env:ES_PASSWORD}
                  key.ignore: false
                  schema.ignore: true
                  write.method: insert
                  behavior.on.null.values: delete
                  flush.synchronously: true
                  transforms: route,extractKey
                  transforms.route.regex: kafka-connect-cdc-main.pg.(.*)
                  transforms.route.replacement: $1
                  transforms.route.type: org.apache.kafka.connect.transforms.RegexRouter
                  transforms.extractKey.type: org.apache.kafka.connect.transforms.ExtractField$Key
                  transforms.extractKey.field: id

              pg.sink-rs-backend-organizations:
                type: sink
                class: io.debezium.connector.jdbc.JdbcSinkConnector
                tasksMax: 3
                config:
                  plugin.name: pgoutput
                  schema.evolution: none
                  insert.mode: upsert
                  delete.enabled: true
                  primary.key.mode: record_key
                  primary.key.fields: id
                  connection.pool.min_size: 1
                  connection.pool.max_size: 2
                  connection.pool.acquire_increment: 1
                  connection.password: ${env:PG_PASSWORD}
                  connection.url: ********************************************************************************************************************
                  connection.username: connector
                  topics: kafka-connect-cdc-main.pg.rs-backend.public.organizations
                  table.name.format: organizations
                  field.include.list: >-
                    id,
                    name,
                    is_food_facility,
                    country,
                    postal,
                    latitude,
                    longitude,
                    created_at,
                    updated_at,
                    created_by,
                    updated_by,
                    abbr,
                    province,
                    city,
                    description,
                    social_networks,
                    address,
                    logo_id,
                    type,
                    business_partner_types,
                    sms_office_id,
                    meta,
                    lead_manager

              pg.sink-rs-backend-organizations-users:
                type: sink
                class: io.debezium.connector.jdbc.JdbcSinkConnector
                tasksMax: 3
                config:
                  plugin.name: pgoutput
                  schema.evolution: none
                  insert.mode: upsert
                  delete.enabled: true
                  primary.key.mode: record_key
                  primary.key.fields: id
                  connection.pool.min_size: 1
                  connection.pool.max_size: 2
                  connection.pool.acquire_increment: 1
                  connection.password: ${env:PG_PASSWORD}
                  connection.url: ********************************************************************************************************************
                  connection.username: connector
                  topics: kafka-connect-cdc-main.pg.rs-backend.public.organizations_users
                  table.name.format: organizations_users
                  field.include.list: >-
                    organization_id,
                    user_id,
                    status,
                    created_by,
                    updated_by,
                    created_at,
                    updated_at,
                    id,
                    is_contact_person,
                    is_external

              pg.sink-rs-backend-organization-user-roles:
                type: sink
                class: io.debezium.connector.jdbc.JdbcSinkConnector
                tasksMax: 3
                config:
                  plugin.name: pgoutput
                  schema.evolution: none
                  insert.mode: upsert
                  delete.enabled: true
                  primary.key.mode: record_key
                  primary.key.fields: role_id,organization_user_id
                  connection.pool.min_size: 1
                  connection.pool.max_size: 2
                  connection.pool.acquire_increment: 1
                  connection.password: ${env:PG_PASSWORD}
                  connection.url: ********************************************************************************************************************
                  connection.username: connector
                  topics: kafka-connect-cdc-main.pg.rs-backend.public.organization_user_roles
                  table.name.format: organization_user_roles
                  field.include.list: >-
                    role_id,
                    created_at,
                    updated_at,
                    organization_user_id,
                    created_by,
                    updated_by

              pg.sink-rs-backend-users:
                type: sink
                class: io.debezium.connector.jdbc.JdbcSinkConnector
                tasksMax: 3
                config:
                  plugin.name: pgoutput
                  schema.evolution: none
                  insert.mode: upsert
                  delete.enabled: true
                  primary.key.mode: record_key
                  primary.key.fields: id
                  connection.pool.min_size: 1
                  connection.pool.max_size: 2
                  connection.pool.acquire_increment: 1
                  connection.password: ${env:PG_PASSWORD}
                  connection.url: ********************************************************************************************************************
                  connection.username: connector
                  topics: kafka-connect-cdc-main.pg.rs-backend.public.users
                  table.name.format: users
                  field.include.list: >-
                    id,
                    email,
                    password,
                    first_name,
                    last_name,
                    is_email_verified,
                    created_at,
                    updated_at,
                    sms_user_id,
                    is_sys_admin,
                    employee_id,
                    phone,
                    work_phone,
                    first_login,
                    konfya_user_id,
                    title,
                    has_acknowledged_privacy

              pg.sink-rs-backend-roles:
                type: sink
                class: io.debezium.connector.jdbc.JdbcSinkConnector
                tasksMax: 3
                config:
                  plugin.name: pgoutput
                  schema.evolution: none
                  insert.mode: upsert
                  delete.enabled: true
                  primary.key.mode: record_key
                  primary.key.fields: id
                  connection.pool.min_size: 1
                  connection.pool.max_size: 2
                  connection.pool.acquire_increment: 1
                  connection.password: ${env:PG_PASSWORD}
                  connection.url: ********************************************************************************************************************
                  connection.username: connector
                  topics: kafka-connect-cdc-main.pg.rs-backend.public.roles
                  table.name.format: roles
                  field.include.list: >-
                    id,
                    name,
                    code,
                    is_internal,
                    active,
                    created_at,
                    updated_at,
                    created_by,
                    updated_by,
                    level,
                    available_in_scheduling

              pg.sink-sight-be-assets-attribute:
                type: sink
                class: io.debezium.connector.jdbc.JdbcSinkConnector
                tasksMax: 3
                config:
                  plugin.name: pgoutput
                  schema.evolution: none
                  insert.mode: upsert
                  delete.enabled: true
                  primary.key.mode: record_key
                  primary.key.fields: id
                  connection.pool.min_size: 1
                  connection.pool.max_size: 2
                  connection.pool.acquire_increment: 1
                  connection.password: ${env:PG_PASSWORD}
                  connection.url: ********************************************************************************************************************
                  connection.username: connector
                  topics: kafka-connect-cdc-main.pg.sight-be.public.assets_attribute
                  table.name.format: assets_attribute

              pg.sink-sight-be-assets-customidentifier:
                type: sink
                class: io.debezium.connector.jdbc.JdbcSinkConnector
                tasksMax: 3
                config:
                  plugin.name: pgoutput
                  schema.evolution: none
                  insert.mode: upsert
                  delete.enabled: true
                  primary.key.mode: record_key
                  primary.key.fields: id
                  connection.pool.min_size: 1
                  connection.pool.max_size: 2
                  connection.pool.acquire_increment: 1
                  connection.password: ${env:PG_PASSWORD}
                  connection.url: ********************************************************************************************************************
                  connection.username: connector
                  topics: kafka-connect-cdc-main.pg.sight-be.public.assets_customidentifier
                  table.name.format: assets_customidentifier

  environment:
    DJANGO_SETTINGS_MODULE: "_config.settings"
    DEBUG: "False"
    REDIS_DSN: redis://redis-redis-production.external-service.svc.cluster.local:6379/0
    REDIS_MAX_CONNECTIONS: "100"
    REDIS_BROKER_URL: "redis://redis-redis-production.external-service.svc.cluster.local:6379/4"
    CELERY_BROKER_URL: "redis://redis-core-queues.external-service.svc.cluster.local:6379/4"
    CELERY_RESULT_BACKEND: "redis://redis-core-queues.external-service.svc.cluster.local:6379/4"
    QUEUE_NAME: "stripe_sync"
    POSTGRES_HOST: pg-passport-be-main.external-service.svc.cluster.local
    POSTGRES_PORT: "5432"
    SMS_API_URL: http://sms-main.sms.svc.cluster.local
    PRIVATE_API_JWT_ALGORITHMS: HS256
    LOG_LEVEL: INFO
    ALLOWED_HOSTS: "0.0.0.0,127.0.0.1,*"
    ALLOWED_AUTHENTICATION_SERVICES: "rise,hermes,grafana"
    FMS_URL: "http://fms-main.fms.svc.cluster.local/v3"
    FMS_FOLDER: "subscriptions"
    QUALITY_CONTROL_BE_URL: http://sight-be-main.sight-be.svc.cluster.local
    SMS_USERNAME: <EMAIL>
    SMS_BASE_URL: http://auth-router.default.svc.cluster.local/sms/api
    RISE_BULK_ONBOARD_URL: http://rs-backend-main.rs.svc.cluster.local/data-management/organization-onboard
    RISE_ASSOCIATE_ORG_URL: http://rs-backend-main.rs.svc.cluster.local/data-management/organization-association/associate-business-partners
    RISE_REMOVE_ASSOCIATION_URL: http://rs-backend-main.rs.svc.cluster.local/data-management/organization-association/unassociate-business-partners
    RISE_ORG_URL: http://rs-backend-main.rs.svc.cluster.local/api/internal/organizations
    RISE_ORG_ASSOCIATE_ECO_URL: http://rs-backend-main.rs.svc.cluster.local/data-management/add-org-into-ecosystem
    RISE_GET_ORG_BY_ID_URL: http://rs-backend-main.rs.svc.cluster.local/data-management/organizations/%s
    RISE_GET_ORGS_URL: http://rs-backend-main.rs.svc.cluster.local/data-management/organizations
    RISE_GET_USERS_BY_ORG_ID_URL: http://rs-backend-main.rs.svc.cluster.local/data-management/organizations/%s/users
    RISE_BASE_URL: http://rs-backend-main.rs.svc.cluster.local
    INTEGRATION_API_BASE_URL: http://integration-api-main.integration-api.svc.cluster.local/api
    FILE_CACHE_TTL: "86400" # 24h
    NMS_BASE_URL: http://notimanager-main.notimanager.svc.cluster.local/nms/v1
    FRONT_END_BASE_URL: "https://app.inspectorio.com/"
    DRAMATIQ_RESULT_TTL: "600000" # 10min
    DRAMATIQ_DATA_CHUNK_MAX_COUNT: "8"
    DRAMATIQ_DUPLICATE_SEARCH_ACTOR_TIMEOUT: "600000" # 10min
    CELERY_DATA_CHUNK_MAX_COUNT: "8"
    CELERY_DUPLICATE_SEARCH_TIMEOUT: "600"
    BULK_DUPLICATE_SEARCH_FILE_MAX_SIZE: "2097152" # 2MB
    BULK_DUPLICATE_SEARCH_FILE_ALLOWED_EXT: "xlsx"
    BULK_DUPLICATE_SEARCH_RISE_MAX_ROWS: "50"
    BULK_DUPLICATE_SEARCH_SIGHT_MAX_ROWS: "100"
    BULK_DUPLICATE_SEARCH_CHUNKS_THRESHOLD: "20"
    BULK_DUPLICATE_SEARCH_CACHE_TTL: "600"
    NUMBER_OF_SIMULTANEOUSLY_TASK_TO_SEARCH: "10"
    SHORT_TOKEN_TTL: "1200"
    SHORT_TOKEN_TTL_ADMIN: "36000"
    DRAMATIQ_PROM_PORT: "9191"
    SENDING_SUBSCRIPTION_TO_RISE_ENABLED: "1"
    ACCESS_DEADLINE_DATE: "30"
    MASTER_DATA_URL: http://master-data-main.master-data.svc.cluster.local
    ADMIN_PANEL_JWT_ALGORITHM: "ES256"
    EXPORT_INSPECTOR_LIMIT_PER_FILE: "5000"
    EXPORT_INSPECTOR_QUEUE_NAME: "export_inspector"
    POSTGRES_USER: passport-be
    POSTGRES_DB: passport-be
    ES_DATASYNC_HOST: http://eck-datasync-pg-es-http.eck-datasync.svc.cluster.local:9200
    ES_DATASYNC_USER: passport-be
    SLACK_CHANNEL_CHARGE_REQUESTS: C08130XKDFE
    FILES_URL: https://files.inspectorio.com
    FILES_CHINA_URL: https://files.inspectorio-platform.com

  environmentFrom:
    - secretRef:
        name: passport-be

  environmentFromSecrets:
    ES_DATASYNC_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: eck-datasync-pg-passport-be-user
          key: password

  externalSecretStore:
    passport-be:
      source:
        data:
          - remoteRef:
              key: pg-passport-be-main
            secretKey: POSTGRES_PASSWORD
          - remoteRef:
              key: pg-passport-be-main-connector
            secretKey: PG_CONNECTOR_PASSWORD
          - remoteRef:
              key: passport-be-sms-jwt-secret-key
            secretKey: SECRET_KEY
          - remoteRef:
              key: passport-be-sms-jwt-secret-key
            secretKey: PRIVATE_API_JWT_DECODE_KEY
          - remoteRef:
              key: passport-be-data_sync_secret
            secretKey: DATA_SYNC_SECRET
          - remoteRef:
              key: passport-be-sms-password
            secretKey: SMS_PASSWORD
          - remoteRef:
              key: passport-be-stripe-api-key
            secretKey: STRIPE_API_KEY
          - remoteRef:
              key: passport-be-stripe-webhook-secret
            secretKey: STRIPE_WEBHOOK_SECRET
          - remoteRef:
              key: passport-be-admin_panel_public_key
            secretKey: ADMIN_PANEL_PUBLIC_KEY
          - remoteRef:
              key: passport-be-integration_api_keys
            secretKey: INTEGRATION_API_KEYS
          - secretKey: SENTRY_DSN
            remoteRef:
              key: passport-be-sentry-default
          - remoteRef:
              key: passport-be-slack_bot_token
            secretKey: SLACK_BOT_TOKEN

    eck-datasync-pg-connector-user:
      template:
        engineVersion: v2
        data:
          username: "{{ .username }}"
          password: "{{ .password }}"
          roles: "{{ .roles }}"
      source:
        dataFrom:
          - extract:
              key: eck-datasync-connector
              conversionStrategy: Default
              decodingStrategy: None

    eck-datasync-pg-passport-be-user:
      template:
        engineVersion: v2
        data:
          username: "{{ .username }}"
          password: "{{ .password }}"
          roles: "{{ .roles }}"
      source:
        dataFrom:
          - extract:
              key: eck-datasync-passport-be
              conversionStrategy: Default
              decodingStrategy: None

  jobs:
    dbmigration:
      ttlSecondsAfterFinished: 60
      disabledGenerateName: true
      annotations:
        argocd.argoproj.io/hook: PreSync
        argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
        argocd.argoproj.io/sync-options: PrunePropagationPolicy=background

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  probes:
    http:
      passport-be-health-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - http://passport-be-main.passport.svc.cluster.local/internal-api/v1/system/health-check
            labels:
              class: saas-outage
              group: passport-be
              domain_type: private
              domain_site: private

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    sms-get:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      rewrite:
        uri: "/api/$(uri_captures[2])"
      route:
        methods:
          - GET
          - OPTIONS
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/(/)?sms/(modules$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/user/inspectors_list$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/user/item/[0-9]+$)"
        - "/~/inspectorio/(/)?sms/(organizations$)"
        - "/~/inspectorio/(/)?sms/(check_user$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/invitation$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/office_mapping/item$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/office_mapping/item_by_factory/[0-9]+$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/check_association$)"
        - "/~/inspectorio/(/)?sms/(subscription/ecosystem$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/contact$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/organization/item/[0-9]+$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/organization/associate$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/organization/relationship$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/organization/sub$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/organization/invitation/[0-9]+/(?:received|sent)$)"
        - "/~/inspectorio/(/)?sms/(user-invitations/[a-f0-9]{8}-?[a-f0-9]{4}-?4[a-f0-9]{3}-?[89ab][a-f0-9]{3}-?[a-f0-9]{12}$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/organization/associates/[0-9]+$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/device$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/api-keys$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/setting/notification$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/contact/organization/[0-9]+$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/organization/subscriber$)"
        - "/~/inspectorio/(/)?sms/(organizations/[0-9]+$)"
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/organization$)"
        - "/~/inspectorio/(/)?sms/(roles)"
        - "/~/inspectorio/(/)?sms/(consent/current)"

    passport-post:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      rewrite:
        uri: "/api/$(uri_captures[2])"
      route:
        methods:
          - POST
        strip_path: true
        regex_priority: 200
      paths:
        - "/~/inspectorio/(/)?sms/(v1/[0-9]+/organization/associate$)"

    restriction:
      enabled: true
      appservice: main
      ingressClass: api
      allow_list: [10.0.0.0/8]
      route:
        methods:
          - GET
        strip_path: true
      paths:
        - "/internal-api/v1/system/ping"
        - "/internal-api/v1/system/health-check"

    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      route:
        methods:
          - POST
          - GET
          - PUT
          - PATCH
          - DELETE
          - OPTIONS
        strip_path: true
      paths:
        - "/psp/"
        - "/internal-api/"

    deny:
      enabled: true
      appservice: main
      ingressClass: api
      allow_list: [10.0.0.0/8]
      route:
        methods:
          - POST
          - GET
          - PUT
          - PATCH
          - DELETE
          - OPTIONS
        strip_path: true
      paths:
        - "/psp/internal-api/"
        - "/psp/prometheus/"
        - "/psp/admin/"
        - "/psp/api/schema/"

    authrouter-get:
      enabled: true
      ingressClass: api
      appservice: main
      customPlugins:
        allow:
          enabled: true
          plugin: internal-ip-restriction
          config:
            allow:
              - 10.0.0.0/8
            message: Forbidden - You don't have permission to access this resource
            status: 403
      route:
        methods:
          - GET
        protocols:
          - http
        regex_priority: 200
      paths:
        - "/~/sms/api/sms/(v1/[0-9]+/user/inspectors_list)$"
        - "/~/sms/api/sms/(v1/[0-9]+/user/item/[0-9]+)$"
        - "/~/sms/api/sms/(modules)$"
        - "/~/sms/api/sms/(organizations)$"
        - "/~/sms/api/sms/(check_user)$"
        - "/~/sms/api/sms/(v1/[0-9]+/invitation)$"
        - "/~/sms/api/sms/(v1/[0-9]+/office_mapping/item)$"
        - "/~/sms/api/sms/(v1/[0-9]+/office_mapping/item_by_factory/[0-9]+)$"
        - "/~/sms/api/sms/(v1/[0-9]+/check_association)$"
        - "/~/sms/api/sms/(subscription/ecosystem)$"
        - "/~/sms/api/sms/(v1/[0-9]+/contact)$"
        - "/~/sms/api/sms/(v1/[0-9]+/organization/item/[0-9]+)$"
        - "/~/sms/api/sms/(v1/[0-9]+/organization/relationship)$"
        - "/~/sms/api/sms/(v1/[0-9]+/organization/sub)$"
        - "/~/sms/api/sms/(v1/[0-9]+/organization/invitation/[0-9]+/(?:received|sent))$"
        - "/~/sms/api/sms/(v1/[0-9]+/organization/associate)$"
        - "/~/sms/api/sms/(v1/[0-9]+/organization)$"
        - "/~/sms/api/sms/(user-invitations/[a-f0-9]{8}-?[a-f0-9]{4}-?4[a-f0-9]{3}-?[89ab][a-f0-9]{3}-?[a-f0-9]{12})$"
        - "/~/sms/api/sms/(v1/[0-9]+/organization/associates/[0-9]+)$"
        - "/~/sms/api/sms/(v1/[0-9]+/device)$"
        - "/~/sms/api/sms/(v1/[0-9]+/api-keys)$"
        - "/~/sms/api/sms/(v1/[0-9]+/setting/notification)$"
        - "/~/sms/api/sms/(v1/[0-9]+/contact/organization/[0-9]+)$"
        - "/~/sms/api/sms/(v1/[0-9]+/organization/subscriber)$"
        - "/~/sms/api/sms/(organizations/[0-9]+)$"
        - "/~/sms/api/sms/(roles)$"
        - "/~/sms/api/sms/(consent/current)$"
      rewrite:
        uri: "/api/$(uri_captures[1])"
      tlsDisabled: true
      hosts:
        - domains:
            - "auth-router.default.svc.cluster.local"

    authrouter-post:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        allow:
          enabled: true
          plugin: internal-ip-restriction
          config:
            allow:
              - 10.0.0.0/8
            message: Forbidden - You don't have permission to access this resource
            status: 403
      route:
        methods:
          - POST
        protocols:
          - http
        regex_priority: 200
      paths:
        - "/~/sms/api/sms/(v1/[0-9]+/office_mapping/item)$"
        - "/~/sms/api/sms/(v1/[0-9]+/setting/multiple_notification)$"
        - "/~/sms/api/sms/(v1/[0-9]+/user/item/[0-9]+)$"
        - "/~/sms/api/sms/(v1/[0-9]+/organization/associate)$"
        - "/~/sms/api/sms/(v1/[0-9]+/organization)$"
        - "/~/sms/api/sms/(ecosystem/organization)$"
        - "/~/sms/api/sms/(v1/[0-9]+/contract-email)$"
      rewrite:
        uri: "/api/$(uri_captures[1])"
      tlsDisabled: true
      hosts:
        - domains:
            - "auth-router.default.svc.cluster.local"

    authrouter-put:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        allow:
          enabled: true
          plugin: internal-ip-restriction
          config:
            allow:
              - 10.0.0.0/8
            message: Forbidden - You don't have permission to access this resource
            status: 403
      route:
        methods:
          - PUT
        protocols:
          - http
        regex_priority: 200
      paths:
        - "/~/sms/api/sms/(v1/[0-9]+/organization/item/[0-9]+)$"
      rewrite:
        uri: "/api/$(uri_captures[1])"
      tlsDisabled: true
      hosts:
        - domains:
            - "auth-router.default.svc.cluster.local"

    authrouter-rw-put: # rw = rewrite
      enabled: true
      appservice: main
      plugins: []
      customPlugins:
        allow:
          enabled: true
          plugin: internal-ip-restriction
          config:
            allow:
              - 10.0.0.0/8
            message: Forbidden - You don't have permission to access this resource
            status: 403
      ingressClass: api
      route:
        methods:
          - PUT
        protocols:
          - http
        regex_priority: 200
      paths:
        - "/~/sms/api/sms/v1/([0-9]+)/organization/item/([0-9]+)$"
      rewrite:
        uri: "/api/v1/$(uri_captures[1])/organization/update/$(uri_captures[2])"
      tlsDisabled: true
      hosts:
        - domains:
            - "auth-router.default.svc.cluster.local"

    authrouter-rw-post: # rw = rewrite
      enabled: true
      ingressClass: api
      appservice: main
      plugins: []
      customPlugins:
        allow:
          enabled: true
          plugin: internal-ip-restriction
          config:
            allow:
              - 10.0.0.0/8
            message: Forbidden - You don't have permission to access this resource
            status: 403
      route:
        methods:
          - POST
        protocols:
          - http
        regex_priority: 200
      paths:
        - "/~/sms/api/sms/v1/([0-9]+)/organization$"
      rewrite:
        uri: "/api/v1/$(uri_captures[1])/organization/create"
      tlsDisabled: true
      hosts:
        - domains:
            - "auth-router.default.svc.cluster.local"

    authrouter-generic:
      enabled: true
      ingressClass: api
      appservice: main
      plugins: []
      customPlugins:
        allow:
          enabled: true
          plugin: internal-ip-restriction
          config:
            allow:
              - 10.0.0.0/8
            message: Forbidden - You don't have permission to access this resource
            status: 403
      route:
        methods:
          - POST
          - GET
          - PUT
          - OPTIONS
          - DELETE
          - PATCH
        protocols:
          - http
        regex_priority: 200
      paths:
        - "/api/v1/generic/"
      tlsDisabled: true
      hosts:
        - domains:
            - "auth-router.default.svc.cluster.local"

  alertrules:
    interval: 1m
    rules:
      CeleryTaskHighFailRate:
        description: More than 5% tasks failed for the task {{ $labels.name }} the past 10m.
        runbook_url: https://inspectoriodocs.atlassian.net/wiki/x/tICj3
        summary: Celery high task fail rate {{ $labels.group }}.
        severity:
          warning:
            expr: &celery-task-high-fail-rate |-
              (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="passport-be",name!~"None",queue_name!~"None"}[10m])) / (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="passport-be",name!~"None",queue_name!~"None"}[10m]))+sum by (group, queue_name, name) (increase(celery_task_succeeded_total{group="passport-be",name!~"None",queue_name!~"None"}[10m])))*100) > 5
            for: 10m
          error:
            expr: *celery-task-high-fail-rate
            for: 25m
          critical:
            expr: *celery-task-high-fail-rate
            for: 40m
            labels:
              class: saas-outage

celery-advanced-exporter:
  enabled: true
  environment:
    - name: CE_BROKER_URL
      value: redis://redis-core-queues.external-service.svc.cluster.local:6379/4
    - name: CE_RETRY_INTERVAL
      value: 5
