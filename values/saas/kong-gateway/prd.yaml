ingress-api:
  enabled: true

  extraLabels:
    app.inspectorio.com/cloud-provider: gcp
    app.inspectorio.com/cloud-region: asia-northeast1
    app.inspectorio.com/cloud-project: inspectorio-production

  podLabels:
    app.inspectorio.com/cloud-provider: gcp
    app.inspectorio.com/cloud-region: asia-northeast1
    app.inspectorio.com/cloud-project: inspectorio-production

  # -----------------------------------------------------------------------------
  # Kong parameters
  # -----------------------------------------------------------------------------
  # Ref: https://docs.konghq.com/gateway/2.8.x/reference/configuration/
  env:
    nginx_upstream_keepalive_timeout: "302s"
    nginx_upstream_keepalive_requests: "1000"
    nginx_http_client_body_buffer_size: "2m"
    nginx_proxy_client_body_timeout: "302s"
    nginx_proxy_client_header_timeout: "302s"
    nginx_proxy_proxy_send_timeout: "302s"
    nginx_proxy_proxy_read_timeout: "302s"
    proxy_access_log: /dev/null json
    proxy_error_log: /dev/stderr
    plugins: bundled,bouncer-auth,fms-auth,stdout-log,jwt-to-header,internal-ip-restriction,jwt-auth
    nginx_proxy_more_set_headers:
      "-s '403 404 405 500 503 502' 'X-Frame-Options: DENY';
      more_set_headers -s '200' 'X-Frame-Options: SAMEORIGIN'; more_set_headers
      -s '204' 'Content-Length: 0'; more_set_headers -s '403 404 405 500
      503 502' 'X-Content-Type-Options: nosniff'; more_set_headers -s '403
      404 405 500 503 502' 'X-XSS-Protection: 1; mode=block'; more_set_headers
      -s '403 404 405 500 503 502' 'Strict-Transport-Security: max-age=31536000;
      includeSubdomains; preload'"
    nginx_http_log_format: 'json ''{"@type": "nginx", ''
      ''"http_host": "$http_host", ''
      ''"remote_addr": "$remote_addr", ''
      ''"time_local": "$time_local", ''
      ''"request": "$request", ''
      ''"request_length": "$request_length", ''
      ''"content_length": "$content_length", ''
      ''"connection_requests": "$connection_requests", ''
      ''"status": "$status", ''
      ''"body_bytes_sent": "$body_bytes_sent", ''
      ''"http_referer": "$http_referer", ''
      ''"http_user_agent": "$http_user_agent", ''
      ''"http_x_forwarded_for": "$http_x_forwarded_for", ''
      ''"request_time": "$request_time", ''
      ''"upstream_response_time": "$upstream_response_time", ''
      ''"upstream_addr": "$upstream_addr", ''
      ''"akamai_header": "$http_X_Akamai_Edgescape", ''
      ''"auth": "$jwt_payload", ''
      ''"auth_username": "$jwt_payload_username", ''
      ''"auth_is_sight_user": "$jwt_payload_is_sight_user", ''
      ''"auth_is_rise_user": "$jwt_payload_is_rise_user", ''
      ''"jwt_payload_org_id": "$jwt_payload_org_id" }'''
    nginx_proxy_set: "$jwt_payload '-'; set $jwt_payload_username '-'; set $jwt_payload_is_rise_user '-'; set $jwt_payload_is_sight_user '-'; set $jwt_payload_org_id '-'"
    trusted_ips: "*********/14, ************/22, ************/22, **********/22, **********/13, **********/14, *************/18, **********/22, ************/18, ***********/15, **********/13, ************/20, ************/20, ************/20, *************/22, ************/17, 2400:cb00::/32, 2606:4700::/32, 2803:f800::/32, 2405:b500::/32, 2405:8100::/32, 2a06:98c0::/29, 2c0f:f248::/32" # Cluster pod ipv4 range and Cloudflare Proxy: https://www.cloudflare.com/ips/
    # injection_propagation_styles:
    # - datadog
    # - tracecontext

  customEnv:
    # DD_AGENT_HOST: datadog-gateway.datadog.svc.cluster.local
    # DD_TRACE_AGENT_PORT: 8129
    # DD_TRACE_AGENT_URL: http://datadog-gateway.datadog.svc.cluster.local:8129
    # DD_SERVICE: kong-api
    # DD_ENV: prd
    # DD_VERSION: 3.23.1-beta.1
    hermes_be_jwt_secret:
      valueFrom:
        secretKeyRef:
          key: HERMES_BE_JWT_SECRET
          name: kong-gateway-ingress-integration-secrets

  # Kong pod count
  replicaCount: 7

  proxy:
    loadBalancerIP: "************" # kong-ingress-api-gateway

  # -----------------------------------------------------------------------------
  # Ingress Controller parameters
  # -----------------------------------------------------------------------------
  ingressController:
    resources:
      limits:
        cpu: 2
        memory: 2Gi
      requests:
        cpu: 0.2
        memory: 448Mi

  resources:
    limits:
      cpu: 4
      memory: 6Gi
    requests:
      cpu: 0.5
      memory: 2Gi

########################################################
# Kong Ingress Integration Hybrid Mode: Control Plane
# Short suffix: `cp` = `control plane`
########################################################
ingress-integration-cp:
  enabled: true

  extraLabels:
    app.inspectorio.com/cloud-provider: gcp
    app.inspectorio.com/cloud-region: asia-northeast1
    app.inspectorio.com/cloud-project: inspectorio-production

  podLabels:
    app.inspectorio.com/cloud-provider: gcp
    app.inspectorio.com/cloud-region: asia-northeast1
    app.inspectorio.com/cloud-project: inspectorio-production

  env:
    # pass environment to plugin apikey-auth works, read from `customEnv.*`
    nginx_main_env: "HERMES_BE_SECRET" # if want multiple supported: "HERMES_BE_BASE_URL; env HERMES_BE_SECRET"
    pg_host: "pg-kong-gateway-integration.external-service.svc.cluster.local"
    pg_ssl: "on"
    plugins: bundled,bouncer-auth,fms-auth,stdout-log,jwt-to-header,apikey-auth
    #log_level: "debug"
    # injection_propagation_styles:
    # - datadog
    # - tracecontext

  customEnv:
    kong_pg_password:
      valueFrom:
        secretKeyRef:
          key: POSTGRES_USER_PASSWORD
          name: kong-gateway-ingress-integration-secrets
    hermes_be_secret:
      valueFrom:
        secretKeyRef:
          key: HERMES_BE_API_TOKEN
          name: kong-gateway-ingress-integration-secrets
    # DD_AGENT_HOST: datadog-gateway.datadog.svc.cluster.local
    # DD_TRACE_AGENT_PORT: 8129
    # DD_TRACE_AGENT_URL: http://datadog-gateway.datadog.svc.cluster.local:8129
    # DD_SERVICE: kong-integration-cp
    # DD_ENV: prd
    # DD_VERSION: 3.23.1-beta.1

########################################################
# Kong Ingress Integration Hybrid Mode: Data Plane
# No suffix `dp` , no need
########################################################
ingress-integration:
  enabled: true

  extraLabels:
    app.inspectorio.com/cloud-provider: gcp
    app.inspectorio.com/cloud-region: asia-northeast1
    app.inspectorio.com/cloud-project: inspectorio-production

  podLabels:
    app.inspectorio.com/cloud-provider: gcp
    app.inspectorio.com/cloud-region: asia-northeast1
    app.inspectorio.com/cloud-project: inspectorio-production

  env:
    # pass environment to plugin apikey-auth works, read from `customEnv.*`
    nginx_main_env: "HERMES_BE_SECRET" # if want multiple supported: "HERMES_BE_BASE_URL; env HERMES_BE_SECRET"
    nginx_upstream_keepalive_timeout: "302s"
    nginx_upstream_keepalive_requests: "1000"
    nginx_http_client_body_buffer_size: "2m"
    nginx_proxy_client_body_timeout: "302s"
    nginx_proxy_client_header_timeout: "302s"
    nginx_proxy_proxy_send_timeout: "302s"
    nginx_proxy_proxy_read_timeout: "302s"
    proxy_access_log: /dev/null json
    plugins: bundled,bouncer-auth,fms-auth,stdout-log,jwt-to-header,apikey-auth,internal-ip-restriction
    nginx_proxy_more_set_headers:
      "-s '403 404 405 500 503 502' 'X-Frame-Options: DENY';
      more_set_headers -s '200' 'X-Frame-Options: SAMEORIGIN'; more_set_headers
      -s '204' 'Content-Length: 0'; more_set_headers -s '403 404 405 500
      503 502' 'X-Content-Type-Options: nosniff'; more_set_headers -s '403
      404 405 500 503 502' 'X-XSS-Protection: 1; mode=block'; more_set_headers
      -s '403 404 405 500 503 502' 'Strict-Transport-Security: max-age=31536000;
      includeSubdomains; preload'"
    nginx_http_log_format: 'json ''{"@type": "nginx", ''
      ''"http_host": "$http_host", ''
      ''"remote_addr": "$remote_addr", ''
      ''"time_local": "$time_local", ''
      ''"request": "$request", ''
      ''"request_length": "$request_length", ''
      ''"content_length": "$content_length", ''
      ''"connection_requests": "$connection_requests", ''
      ''"status": "$status", ''
      ''"body_bytes_sent": "$body_bytes_sent", ''
      ''"http_referer": "$http_referer", ''
      ''"http_user_agent": "$http_user_agent", ''
      ''"http_x_forwarded_for": "$http_x_forwarded_for", ''
      ''"request_time": "$request_time", ''
      ''"upstream_response_time": "$upstream_response_time", ''
      ''"upstream_addr": "$upstream_addr", ''
      ''"akamai_header": "$http_X_Akamai_Edgescape", ''
      ''"auth": "$jwt_payload", ''
      ''"auth_username": "$jwt_payload_username", ''
      ''"auth_is_sight_user": "$jwt_payload_is_sight_user", ''
      ''"auth_is_rise_user": "$jwt_payload_is_rise_user", ''
      ''"jwt_payload_org_id": "$jwt_payload_org_id" }'''
    nginx_proxy_set: "$jwt_payload '-'; set $jwt_payload_username '-'; set $jwt_payload_is_rise_user '-'; set $jwt_payload_is_sight_user '-'; set $jwt_payload_org_id '-'"
    real_ip_header: "X-Forwarded-For"
    real_ip_recursive: "on"
    trusted_ips: "*********/14, ************/22, ************/22, **********/22, **********/13, **********/14, *************/18, **********/22, ************/18, ***********/15, **********/13, ************/20, ************/20, ************/20, *************/22, ************/17, 2400:cb00::/32, 2606:4700::/32, 2803:f800::/32, 2405:b500::/32, 2405:8100::/32, 2a06:98c0::/29, 2c0f:f248::/32" # Cluster pod ipv4 range and Cloudflare Proxy: https://www.cloudflare.com/ips/
    #log_level: "debug"
    # injection_propagation_styles:
    # - datadog
    # - tracecontext

  customEnv:
    hermes_be_secret:
      valueFrom:
        secretKeyRef:
          key: HERMES_BE_API_TOKEN
          name: kong-gateway-ingress-integration-secrets
    # DD_AGENT_HOST: datadog-gateway.datadog.svc.cluster.local
    # DD_TRACE_AGENT_PORT: 8129
    # DD_TRACE_AGENT_URL: http://datadog-gateway.datadog.svc.cluster.local:8129
    # DD_SERVICE: kong-integration
    # DD_ENV: prd
    # DD_VERSION: 3.23.1-beta.1

  # Kong pod count
  replicaCount: 3

  proxy:
    loadBalancerIP: "**************" # kong-ingress-integration-gateway

  resources:
    limits:
      cpu: 2
      memory: 2Gi
    requests:
      cpu: 0.25
      memory: 320Mi

# Just for testing staging
ingress-integration-postgresql:
  enabled: false

kong-gateway:
  fullnameOverride: kong-gateway
  serviceAccount:
    create: true
    name: kong-gateway
    enableWorkloadIdentity: true

  externalSecretStore:
    kong-gateway-ingress-integration-cluster-tls:
      template:
        type: kubernetes.io/tls
        engineVersion: v2
        data:
          tls.crt: "{{ .crt }}"
          tls.key: "{{ .key }}"
      source:
        data:
          - secretKey: crt
            remoteRef:
              key: kong-gateway-ingress-integration-cluster-cert
          - secretKey: key
            remoteRef:
              key: kong-gateway-ingress-integration-cluster-key
    kong-gateway-ingress-integration-secrets:
      source:
        data:
          - secretKey: HERMES_BE_API_TOKEN
            remoteRef:
              key: hermes-be-api_token
              version: latest
          - secretKey: HERMES_BE_JWT_SECRET
            remoteRef:
              key: kong-gateway-hermes-be-jwt-secret-key
              version: latest
          - secretKey: POSTGRES_USER_PASSWORD
            remoteRef:
              key: pg-kong-gateway-integration
              version: latest

  appservices:
    gateway-api-domain:
      enabled: true
      service:
        enabled: true
        type: ExternalName
        externalName: api.inspectorio.com
        port: 80
        protocol: TCP

    gateway-integration-domain:
      enabled: true
      service:
        enabled: true
        type: ExternalName
        externalName: integration.inspectorio.com
        port: 80
        protocol: TCP

  kongingress:
    # This root is used for blackbox health checks
    api:
      enabled: true
      appservice: gateway-api-domain
      ingressClass: api
      customPlugins:
        root-status-200:
          enabled: true
          plugin: request-termination
          config:
            message: Healthy
            status_code: 200
      route:
        methods:
          - GET
          - OPTIONS
      paths:
        - "/~/$"
      hosts:
        - domains:
            - "api.inspectorio.com"
            - "kong-api-prod.inspectorio.com"
            - "kong-api.inspectorio-platform.com"
          tlsSecretName: inspectorio-com-common-tls

    integration:
      enabled: true
      appservice: gateway-integration-domain
      ingressClass: integration
      customPlugins:
        root-status-200:
          enabled: true
          plugin: request-termination
          config:
            message: Healthy
            status_code: 200
      route:
        methods:
          - GET
          - OPTIONS
      paths:
        - "/~/$"

  kongalert:
    rules:
      high-5xx-rate:
        enabled: false

  istio:
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: false

  probes:
    http:
      api-uptime:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - https://api.inspectorio.com
            labels:
              class: saas-outage
              group: kong
              domain_type: public
              domain_site: global

      integration-uptime:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - https://integration.inspectorio.com
            labels:
              class: saas-outage
              group: kong
              domain_type: public
              domain_site: global
