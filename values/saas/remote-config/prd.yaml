remote-config:
  generic_chart_name: genapp-v1

  environment:
    API_ENDPOINT: https://api.inspectorio.com/inspectorio/

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  probes:
    http:
      remote-config-health-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - http://remote-config-default-main.remote-config.svc.cluster.local/health
            labels:
              class: saas-outage
              group: remote-config
              domain_type: private
              domain_site: private

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress: {}
    egress: {}

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      route:
        methods:
          - GET
          - OPTIONS
        strip_path: true
      paths:
        - "/inspectorio/v1/remote/config"
      proxy:
        path: "/v1/remote/config"
