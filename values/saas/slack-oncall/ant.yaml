slack-oncall:
  generic_chart_name: genapp-v1

  serviceAccount:
    create: true
    enableWorkloadIdentity: true

  externalSecretStore:
    slack-oncall-secret: # name of k8s secret
      source:
        data:
          - secretKey: PD_TOKEN
            remoteRef:
              key: slack-oncall-pd-token
          - secretKey: SLACK_ACCESS_TOKEN
            remoteRef:
              key: slack-oncall-slack-access-token
          - secretKey: SLACK_USER_ACCESS_TOKEN
            remoteRef:
              key: slack-oncall-slack-user-access-token
          - secretKey: ROOTLY_API_TOKEN
            remoteRef:
              key: slack-oncall-rootly-api-token

  environmentFromSecrets:
    PD_TOKEN:
      valueFrom:
        secretKeyRef:
          name: slack-oncall-secret
          key: PD_TOKEN
    SLACK_ACCESS_TOKEN:
      valueFrom:
        secretKeyRef:
          name: slack-oncall-secret
          key: SLACK_ACCESS_TOKEN
    SLACK_USER_ACCESS_TOKEN:
      valueFrom:
        secretKeyRef:
          name: slack-oncall-secret
          key: SLACK_USER_ACCESS_TOKEN
    ROOTLY_API_TOKEN:
      valueFrom:
        secretKeyRef:
          name: slack-oncall-secret
          key: ROOTLY_API_TOKEN
  environment:
    SLACK_COMMON_CHANNELS: CKS09JP6F
    INFRA_SERVICE_REDIS_DEFAULT_HOST: redis-redis-infra.external-service.svc.cluster.local
    INFRA_SERVICE_REDIS_DEFAULT_PORT: 6379
    INFRA_SERVICE_REDIS_DEFAULT_DB: 0
    ROOTLY_SCHEDULE_ID: 637d666b-9e27-4efa-a821-848b95ac8313
    ROOTLY_SLACK_GROUP: sreoncall-temp

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress:
      apps: []
    egress:
      apps: []
      custom:
      - ports:
        - port: 6379
          protocol: TCP
        to:
        - ipBlock:
              cidr: 10.0.0.0/8
