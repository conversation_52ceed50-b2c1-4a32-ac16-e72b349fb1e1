tracking:
  appservices:
    main:
      replicaCount: 2
      containers:
        main:
          resources:
            limits:
              memory: 3Gi
              cpu: 2
            requests:
              memory: 2Gi
              cpu: 0.2
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload='tracking-appservice-main'}) / sum(kube_deployment_status_replicas{deployment='tracking-appservice-main'})"
          interval: 10s # call expr each internval
          name: tracking_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 10
        scalers:
          main:
            enabled: true
            minReplicaCount: 2
            maxReplicaCount: 8
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 50
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 90
                    selectPolicy: Max
            triggers:
              cpu:
                value: "100"

  workers:
    tracking:
      enabled: true
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{queue_name=~'tracking', exported_namespace='tracking'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{hostname=~'.*worker-tracking.*', exported_namespace='tracking'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-tracking.*',le='+Inf', exported_namespace='tracking'}[1m]))-sum(rate(celery_task_runtime_bucket{hostname=~'.*worker-tracking.*',le='2.5', exported_namespace='tracking'}[1m])))or vector(1)))"
          interval: 10s # call expr each internval
          name: tracking_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          query: tracking_workers_hpa_metric{namespace="tracking"}
          threshold: 2
        scalers:
          tracking:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 15
    maintenance:
      enabled: true
    update-milestone-consumer:
      enabled: true
      nodeSelector:
        saas: "true"
      kedaHPA:
        scalers:
          update-milestone-consumer:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 4
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 75
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 120
                    selectPolicy: Max
            triggers:
              cpu:
                value: "100"
    sight-connectivity-consumer:
      enabled: true
      nodeSelector:
        saas: "true"
      kedaHPA:
        scalers:
          sight-connectivity-consumer:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 4
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 75
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 120
                    selectPolicy: Max
            triggers:
              cpu:
                value: "100"
    integration-po-events-consumer:
      enabled: true
      nodeSelector:
        saas: "true"
      kedaHPA:
        scalers:
          integration-po-events-consumer:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 4
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 75
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 120
                    selectPolicy: Max
            triggers:
              cpu:
                value: "100"
    automation-po-events-consumer:
      enabled: true
      containers:
        default:
          resources:
            requests:
              memory: 384Mi
              cpu: 0.2
      nodeSelector:
        saas: "true"
      kedaHPA:
        scalers:
          automation-po-events-consumer:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 12
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 75
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 120
                    selectPolicy: Max
            triggers:
              cpu:
                value: "100"
  environment:
    INSPECTION_PUBSUB_TOPIC: sight_inspection_prod
    DATABASE_URL: "postgis://tracking:$(POSTGRES_OWNER_PASSWORD)@pgbouncer-sight-be-main.pgbouncer.svc.cluster.local:5432/sight-be"
    REPLICA_DATABASE_URL: "postgis://tracking:$(POSTGRES_OWNER_PASSWORD)@pgbouncer-sight-be-main-replica.pgbouncer.svc.cluster.local:5432/sight-be"
    USE_REPLICA: "true"
    SMS_DATABASE_URL: "***************************************************************************************************************/passport-be?sslmode=require&sslcert=/tmp/dummycertpath"
    WORKFLOW_ENGINE_DATABASE_URL: "*********************************************************************************************************************************/workflow-engine?sslmode=require&sslcert=/tmp/dummycertpath"
    INTERNAL_IPS: "127.0.0.1,************,*************,**************,*************,*************"
    SMS_BASE_URL: http://auth-router.default.svc.cluster.local/sms/api/sms
    NOTIMANAGER_BASE_URL: http://notimanager-main.notimanager.svc.cluster.local/nms/v1
    SIGHT_BE_URL: http://sight-be-main.sight-be.svc.cluster.local/inspectorio
    CAPA_BASE_URL: http://car-main.car.svc.cluster.local/v1
    ANALYTIC3_BASE_URL: http://analytic3-main.analytic3.svc.cluster.local
    AMS_BASE_URL: http://assignments-gcp-prd-assignments.default.svc.cluster.local/assignments
    WEB_APP_URL: https://app.inspectorio.com
    DEFECT_PREDICTION_BASE_URL: http://defect-recommend-be-main.defect-recommend-be.svc.cluster.local
    IMS_BASE_URL: http://inspections-gcp-prd-inspections.default.svc.cluster.local/inspections
    REPORT_DETAIL_URL: https://app.inspectorio.com/inspections/report-html-internal
    FACTORY_RISK_BASE_URL: http://factory-risk-be-main.factory-risk-be.svc.cluster.local
    WALMART_FACTORY_RISK_BASE_URL: placeholder
    FMS_BASE_URL: http://fms-main.fms.svc.cluster.local
    REPORT_HTML_BASE_URL: http://report-html-main.report-html.svc.cluster.local
    TRANSLATION_BASE_URL: http://translation-main.translation.svc.cluster.local
    MASTER_DATA_BASE_URL: http://master-data-main.master-data.svc.cluster.local
    PRODUCT_RISK_BASE_URL: http://product-risk-be-main.product-risk.svc.cluster.local
    ELASTICSEARCH_HOST: http://eck-sight-monitoring-es-http.eck-sight.svc.cluster.local:9200
    AUTO_SHARE_PO_ORGS: "292085,291290,293339"
    UNAVAILABLE_PLACEHOLDER_ORG: "315851"
    WALMART_RETAILER_ORG_ID: "341403"
    CACHE_URL: "redis://redis-sight-caches.external-service.svc.cluster.local:6379/7"
    CELERY_BROKER_URL: "redis://redis-sight-queues.external-service.svc.cluster.local:6379/7"
    INTEGRATION_RULE_BUILDER_KAFKA_BOOTSTRAP_SERVERS: kafka-main-kafka-bootstrap.kafka.svc.cluster.local
    UPDATE_MILESTONE_GROUP_ID: tracking.master_data
    UPDATE_MILESTONE_TOPIC: tracking.master_data
    CELERY_LOG_LEVEL: "ERROR"
    WORKER_PROCESSES: "1"
    WORKER_CONCURRENCY: "2"
    WORKER_AUTOSCALE: "10"
    SMS_USERNAME: "<EMAIL>"
    INSPECTION_NS_KAFKA_URL: kafka-main-kafka-bootstrap.kafka.svc.cluster.local:9092

  environmentFrom:
    - secretRef:
        name: lst-tracking

  externalSecretStore:
    lst-tracking: # name of k8s secret
      source:
        data:
          - secretKey: POSTGRES_OWNER_PASSWORD
            remoteRef:
              key: pg-sight-be-main-tracking
              version: latest
          - secretKey: POSTGRES_PASSPORT_PASSWORD
            remoteRef:
              key: pg-passport-be-main-tracking
              version: latest
          - secretKey: POSTGRES_WORKFLOW_ENGINE_PASSWORD
            remoteRef:
              key: pg-workflow-engine-main
              version: latest
          - secretKey: DATA_SYNC_SECRET
            remoteRef:
              key: tracking-data-sync-secret
              version: latest
          - secretKey: DEBUG_SECRET
            remoteRef:
              key: tracking-debug-secret
              version: latest
          - secretKey: GOOGLE_MAPS_API_KEY
            remoteRef:
              key: tracking-google-maps-api-key
              version: latest
          - secretKey: SECRET_KEY
            remoteRef:
              key: passport-be-sms-jwt-secret-key
              version: latest
          - secretKey: JWT_SECRET_KEY
            remoteRef:
              key: passport-be-sms-jwt-secret-key
              version: latest
          - secretKey: SMS_PASSWORD
            remoteRef:
              key: tracking-sms-password
              version: latest
          - secretKey: FMS_API_KEY
            remoteRef:
              key: tracking-fms-api-key
              version: latest
          - secretKey: MIXPANEL_PROJECT_TOKEN
            remoteRef:
              key: tracking-mixpanel-token
              version: latest
          - secretKey: SENTRY_DSN
            remoteRef:
              key: tracking-sentry-default
              version: latest

  jobs:
    migration:
      enabled: true
      ttlSecondsAfterFinished: 60
      disabledGenerateName: true
      annotations:
        argocd.argoproj.io/hook: PreSync
        argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
        argocd.argoproj.io/sync-options: PrunePropagationPolicy=background
    publish-event-bus-events:
      ttlSecondsAfterFinished: 60
      disabledGenerateName: true
      annotations:
        argocd.argoproj.io/hook: PreSync
        argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
        argocd.argoproj.io/sync-options: PrunePropagationPolicy=background
  
  cronjobs:
    overdue-daily:
      activeDeadlineSeconds: 7200
    late-done-daily:
      activeDeadlineSeconds: 7200
    

    
  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true
  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress:
      apps: # <namespace>:<app-name>
        - "integration-api:integration-api"
    egress:
      apps: # <namespace>:<app-name>
        - "eck-sight:eck-sight-monitoring"
        - "analytic3:analytic3"
        - "car:car"
        - "defect-recommend-be:defect-recommend-be"
        - "factory-risk-be:factory-risk-be"
        - "fms:fms"
        - "notimanager:notimanager"
        - "product-risk-be:product-risk-be"
        - "report-html:report-html"
        - "translation:translation"
        - "sight-be:sight-be"
        - "master-data:master-data"
        - "kafka-schema-registry:kafka-schema-registry"
      custom:
        - ports:
          - port: 9092
            protocol: TCP
          to:
          - namespaceSelector:
              matchLabels:
                name: kafka
            podSelector:
              matchLabels:
                app.kubernetes.io/instance: kafka-main
                app.kubernetes.io/name: kafka
        - to:
          - ipBlock:
              cidr: 10.0.0.0/8
          ports:
          - protocol: TCP
            port: 5432 # postgresql
          - protocol: TCP
            port: 6379 # redis

  infra:
    services:
      kafkacluster:
        main:
          topics:
            revise_po_shipment_window:
              config:
                retention.ms: 604800000 # 7 Days
            master_data:
              partitions: 3
            milestone_action:
              config:
                retention.ms: 1209600000 # 14 Days

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - DELETE
          - PUT
          - PATCH
        strip_path: false
      paths:
        - "/inspectorio/ta"

    options:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - OPTIONS
        strip_path: false
      paths:
        - "/inspectorio/ta"
        - "/~/inspectorio/dss/v1/(.*)/ta"
        - "/~/inspectorio/workflow-engine"

    dss-ta:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
          - PATCH
        strip_path: false
        regex_priority: 310 # higher than qcbe service regex-priority
      paths:
        - "/~/inspectorio/dss/v1/(.*)/ta"

    workflow-engine:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: [ "bouncer-auth-default" ]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
          - PATCH
        strip_path: false
      paths:
        - "/~/inspectorio/workflow-engine"

  alertrules:
    interval: 1m
    rules:
      CeleryTaskHighFailRate:
        description: More than 5% tasks failed for the task {{ $labels.name }} the past 10m.
        runbook_url: https://inspectoriodocs.atlassian.net/wiki/x/tICj3
        summary: Celery high task fail rate {{ $labels.group }}.
        severity:
          warning:
            expr: &celery-task-high-fail-rate |-
              (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="tracking",name!~"None",queue_name!~"None"}[10m])) / (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="tracking",name!~"None",queue_name!~"None"}[10m]))+sum by (group, queue_name, name) (increase(celery_task_succeeded_total{group="tracking",name!~"None",queue_name!~"None"}[10m])))*100) > 5
            for: 10m
          error:
            expr: *celery-task-high-fail-rate
            for: 25m
          critical:
            expr: *celery-task-high-fail-rate
            for: 40m
            labels:
              class: saas-outage

celery-advanced-exporter:
  enabled: true
  resources:
    limits:
      cpu: 1.5
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 128Mi
  environment:
    - name: CE_BROKER_URL
      value: redis://redis-sight-queues.external-service.svc.cluster.local:6379/7
    - name: CE_RETRY_INTERVAL
      value: 5
