# https://gitlab.inspectorio.com/saas/sight/integration/dedupe-api
dedupe-api:
  environment:
    UWSGI_PROCESSES: 4

  appservices:
    main:
      replicaCount: 2
      containers:
        main:
          resources:
            limits:
              cpu: 1
              memory: 4Gi
            requests:
              cpu: 10m
              memory: 256Mi

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress:
      apps:
        - "rs:rs-backend"
    egress:
      apps: []
      custom:
        - to:
          - ipBlock:
              cidr: 10.0.0.0/8
          ports:
          - protocol: TCP
            port: 5432

  istio:
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  kongingress:
    options:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - OPTIONS
        strip_path: true
      paths:
        - /inspectorio/dedupe/

    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - GET
        strip_path: true
      paths:
        - /inspectorio/dedupe/
