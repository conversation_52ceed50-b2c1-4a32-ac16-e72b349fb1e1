sms:
  appservices:
    main:
      replicaCount: 3
      containers:
        main:
          resources:
            limits:
              cpu: 2
              memory: 5Gi
            requests:
              cpu: 200m
              memory: 2Gi
        uwsgi-exporter:
          resources:
            limits:
              cpu: 100m
              memory: 128Mi
            requests:
              cpu: 20m
              memory: 32Mi
      configmap:
        name: sms-credentials-sharing
        values:
          orgids: "294178\n303731\n293407\n340629\n293339\n300844\n308435\n"
          useremails: "<EMAIL>"
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload='sms-appservice-main'}) / sum(kube_deployment_status_replicas{deployment='sms-appservice-main'})"
          interval: 15s # call expr each internval
          name: sms_per_pod_rps # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 30
        scalers:
          main:
            enabled: true
            minReplicaCount: 4
            maxReplicaCount: 17
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 75
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 120
                    selectPolicy: Max
            triggers:
              cpu:
                value: 90

  workers:
    beat:
      containers:
        main:
          environment:
            SIGHT_ZENDESK_API_USER: <EMAIL>
            LOGGING_LEVEL: INFO
          resources:
            limits:
              cpu: 0.5
              memory: 512Mi
            requests:
              cpu: 0.1
              memory: 128Mi
    default:
      replicaCount: 2
      containers:
        main:
          environment:
            LOGGING_LEVEL: INFO
          resources:
            limits:
              cpu: 2
              memory: 4Gi
            requests:
              cpu: 0.5
              memory: 1Gi
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{queue_name=~'sms'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{hostname=~'.*sms-worker.*'}[1m])>0))*5)*1-(sum(rate(celery_task_runtime_bucket{hostname=~'.*sms-worker.*',le='+Inf'}[1m]))-sum(rate(celery_task_runtime_bucket{hostname=~'.*sms-worker.*',le='2.5'}[1m])))or vector(1)))"
          interval: 15s # call expr each internval
          name: sms_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
        scalers:
          worker:
            enabled: true
            minReplicaCount: 2
            maxReplicaCount: 7

  environment:
    REDIS_URI: redis://redis-redis-production.external-service.svc.cluster.local:6379/0
    DEBUG: "False"
    ANALYTIC_AWS_STREAM_NAME: inspection_prod
    FRONT_END_SYSTEM_BASE_URL: https://app.inspectorio.com/
    TOTANGO_STATUS: Paying
    HINSPECTOR_SERVER: hinspector-prod.ins-internal
    HINSPECTOR_PORT: "80"
    DOCUSIGN_ACCOUNT_ID: 6e778ef9-b108-4013-a062-1f849ce2bb56
    DOCUSIGN_ENV: na3
    DOCUSIGN_OAUTH_BASE_URL: account.docusign.com
    DOCUSIGN_PATH_PRIVATE_KEY: /app/private-key/docusign_private.txt
    CURRENT_DOCUSIGN_SIGNING_YEAR: 2018
    DOCUSIGN_URL: https://na3.docusign.net/restapi
    SUPPORT_CONTACT_EMAIL: <EMAIL>
    SALES_CONTACT_EMAIL: <EMAIL>
    MIGRATION_TARGET_ID: "252437"
    INTELLUM_URL: https://inspectorio.exceedlms.com/api/v2/users
    INTERGRATION_NOTI_DARK_FEATURE_CODE: notification_integrations
    NGINX_WORKER: 2
    ONE_ORG_BASE_URL: ""
    SMS_PUBLIC_BASE_URL: https://api.inspectorio.com/inspectorio/sms/v1
    DOCEBO_ENABLE: "1"
    DOCEBO_URL: https://inspectorio.docebosaas.com
    DOCEBO_CLIENT_ID: Inspectorio
    DOCUSIGN_TEMPLATE_SUBSCRIPTION_ID: 7d37a78e-fd52-41f1-b869-8623f962539e
    STRIPE_ADDON_LIST: "smartcolor,smarttape,taskautomation,dynamicrisk,ppmanalytics,bookinganalytics,singlesignon,apiintegration,configurableautomationrules,adhocanalytics,bookingcreditplus"
    MAX_DISCOUNT_FOR_0_TO_10_USER: "0.1"
    MAX_DISCOUNT_FOR_11_TO_30_USER: "0.15"
    MAX_DISCOUNT_FOR_31_TO_60_USER: "0.2"
    MAX_DISCOUNT_FOR_OVER_60_USER: "0.3"
    CDN_BASE_URL: "https://inspectorio-platform.com"
    CDN_FRONTEND_SUBDOMAIN: "app"
    FRONT_END_BASE_URL: "https://app.inspectorio.com/"
    PORTAL_BASE_URL: "https://portal.inspectorio.com/"
    EXTENDED_LOGGER_CHUNK_SIZE: "8000"
    IS_AUTO_REMIND_RENEW_ENABLED: "1"
    KEY_ACCOUNTS: "310787,319236,341383,346760,343948,342414,311950,344208,311569,313362,339987,339986,252437,293781,294165,341272,316189,340893,293409,313249,293413,312358,318253,323630,300846,300847,311732,339509,375478,342839,294074,316603,316604,339771,311233,322500,344522,292298,345421,357967,308432,320468,340565,344539,340699,339805,324449,317538,345953,316772,346217,312556,302702,339698,292085,307966,355967,375906,375899,379454,375896,375990,311479,379449,379452,375988,294013,300722,375770,375895,375986,375900,307972,293354,308325,379455,292677,375755,379456,379458,375987,375905,375903,379450,375904,375754,375991,375902,375482,375751,375753,311384,375752,375639,380226,380132,292788,379448,380133,380128,379451,380129,380130,313150,380237,380127,376022,376020,380135,380227,380232,380539,340704,380425,380229,380430,380230,380234,380224,380236,380231,380235,380233,380243,380238,380223,380429,380059,380727,380542,380721,380725,380715,380548,380724,380547,380543,381047,380531,380418,380417,380720,380718,380714,380713,380540,381044,380726,380716,380717,380545,381045,380719,380541,380723,293075,356069,389192,381333,381321,381215,381046,381211,381210,381318,381214,381325,381209,381320,381323,381319,381335,381213,381043,381212,381217,381334,381049"
    IUBENDA_TERMS_ID_EN: "62492382"
    IUBENDA_TARGET_ECOSYSTEM_ID: "252437"
    IUBENDA_TARGET_TERMS_ID: "97322786"
    ALLOWED_AUTHENTICATION_SERVICES: "rise,hermes,grafana,docuflow,jimmy"
    IS_DUNNING_NOTICE_ENABLED: "1"
    DUNNING_NOTICE_START_DATE: "2021-04-01"
    CONFIG_SIM_SESSIONS: '{"web": 2, "mobile": 1}'
    SMS_USERNAME: <EMAIL>
    SENTRY_ENV: prd
    SHORT_TOKEN_TTL: "1200"
    SHORT_TOKEN_TTL_ADMIN: "36000"
    EXPIRED_SESSION_TTL: "1296000"
    USE_GO_FOR_STRIPE_CUSTOMER: "1"
    SSO_REDIRECT_URL: "https://id.inspectorio.com/sso"
    SSO_EMAIL_DOMAIN_ALLOW_LIST: "qa.team"
    CELERY_BROKER_URL: redis://redis-redis-production.external-service.svc.cluster.local:6379/1
    WECHAT_APP_ID: wx683fa0865b6e8c35
    CHINESE_WECHAT_APP_ID: wxab740a0313a47004
    DOCUSIGN_USER_ID: 70411a6c-d439-49b6-b305-e4439740b20c
    DOCEBO_SUPERADMIN_USERNAME: <EMAIL>
    ZENDESK_API_USER: <EMAIL>
    SIGHT_ZENDESK_API_USER: <EMAIL>
    ZENDESK_DOMAIN: https://inspectorio.zendesk.com/access/jwt?jwt=
    SKIP_LOGIN_RESTRICTION: "True"
  
  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps:
      - bouncers:bouncers
      - fms:fms
      - hermes:hermes-be
      - notimanager:notimanager
      - passport:passport-be
      - rs:rs-backend
      - sight-be:sight-be
      custom:
      - ports:
        - port: 5432
          protocol: TCP
        - port: 6379
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
    ingress:
      apps:
      - bouncers:bouncers
      - hermes:hermes-be
      - passport:passport-be
      - permission-dashboard:permission-dashboard
      - report-html:report-html
      - superset:analytics-superset

  externalSecretStore:
    sms: # name of k8s secret
      source:
        data:
          - secretKey: DB_PASSWORD # name of key in k8s secret
            remoteRef:
              key: pg-passport-be-main-sms
          - secretKey: ZENDESK_SHARE_KEY
            remoteRef:
              key: sms-zendesk-share-key
          - secretKey: IUBENDA_API_KEY_EN
            remoteRef:
              key: sms-iubenda-api-key-en
          - secretKey: IUBENDA_TARGET_API_KEY
            remoteRef:
              key: sms-iubenda-target-api-key
          - secretKey: STRIPE_API_KEY
            remoteRef:
              key: sms-stripe-api-key
          - secretKey: STRIPE_WEBHOOK_SECRET
            remoteRef:
              key: sms-stripe-webhook-secret
          - secretKey: DOCUSIGN_INTEGRATOR_KEY
            remoteRef:
              key: sms-docusign-integrator-key
          - secretKey: DOCUSIGN_INTEGRATOR_KEY
            remoteRef:
              key: sms-docusign-integrator-key
          - secretKey: WECHAT_APP_SECRET
            remoteRef:
              key: sms-wechat-app-secret
          - secretKey: CHINESE_WECHAT_APP_SECRET
            remoteRef:
              key: sms-chinese-wechat-app-secret
          - secretKey: DATA_SYNC_SECRET
            remoteRef:
              key: sms-data-sync-secret
          - secretKey: DOCEBO_CLIENT_SECRET
            remoteRef:
              key: sms-docebo-client-secret
          - secretKey: DOCEBO_CLIENT_SECRET
            remoteRef:
              key: sms-docebo-client-secret
          - secretKey: DOCEBO_SUPERADMIN_PASSWORD
            remoteRef:
              key: sms-docebo-superadmin-password
          - secretKey: DOCEBO_DEFAULT_PASSWORD
            remoteRef:
              key: sms-docebo-default-password
          - secretKey: INTELLUM_API_KEY
            remoteRef:
              key: sms-intellum-api-key
          - secretKey: INTELLUM_DEFAULT_PASSWORD
            remoteRef:
              key: sms-intellum-default-password
          - secretKey: SECRET_KEY
            remoteRef:
              key: sms-sms-jwt-secret-key
          - secretKey: SMS_PASSWORD
            remoteRef:
              key: sms-sms-account-api-ams-password
          - secretKey: SIGHT_ZENDESK_TOKEN
            remoteRef:
              key: sms-sight-zendesk-token
          - secretKey: PRIVATE_KEY
            remoteRef:
              key: sms-private-key
          - secretKey: SAMLCERT
            remoteRef:
              key: sms-samlcert
          - secretKey: SAMLPRIVATEKEY
            remoteRef:
              key: sms-samlprivatekey
          - secretKey: HUBSPOT_API_KEY
            remoteRef:
              key: sms-hubspot-api-key
          - secretKey: ZENDESK_API_KEY
            remoteRef:
              key: sms-zendesk-api-key
          - secretKey: SIGHT_ZENDESK_API_KEY
            remoteRef:
              key: sms-sight-zendesk-api-key
          - secretKey: FMS_SERVICE_AUTHENTICATION
            remoteRef:
              key: sms-fms-service-authentication
          - secretKey: HERMES_API_TOKEN
            remoteRef:
              key: sms-hermes-api-key

  jobs:
    migration:
      ttlSecondsAfterFinished: 60
      disabledGenerateName: true
      annotations:
        argocd.argoproj.io/hook: PreSync
        argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
        argocd.argoproj.io/sync-options: PrunePropagationPolicy=background

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    session-revoke-unlock:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        cors-credentials:
          enabled: true
          plugin: cors
          config:
            credentials: true
            exposed_headers:
              - Cache-Control
              - Content-Language
              - Content-Type
              - Expires
              - Last-Modified
              - Pragma
              - location
            headers:
              - User-Agent
              - X-Requested-With
              - If-Modified-Since
              - Cache-Control
              - Content-Type
              - Range
              - auth
              - authorization
              - Saas-Backend
              - x-client
              - rs-auth
              - Pragma
              - service
            methods:
              - POST
              - OPTIONS
            origins:
              - "https://grafana.inspectorio.com"
              - "https://rise.inspectorio.com"
              - "https://app.inspectorio.com"
              - "https://rise.inspectorio-platform.com"
              - "https://app.inspectorio-platform.com"
              - "https://portal.gcp-prd.inspectorio.com"
              - "https://portal.inspectorio.com"
              - "https://kong-kube-prod.inspectorio.com"
              - "https://api.inspectorio.com"
              - "https://kong-api-prod.inspectorio.com"
              - "https://kong-api.inspectorio-platform.com"
            preflight_continue: false
      rewrite:
        uri: "/sms/api/sms/$(uri_captures[1])"
      route:
        methods:
          - POST
          - OPTIONS
        strip_path: true
        regex_priority: 300
      paths:
        - "/~/inspectorio/sms/(security/v1/session/unblock$)"
        - "/~/inspectorio/sms/(security/v1/session/revoke$)"

    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      route:
        methods:
          - PATCH
          - POST
          - GET
          - PUT
          - DELETE
          - OPTIONS
        strip_path: true
        regex_priority: 100
      proxy:
        path: "/sms/api/sms/"
      paths:
        - "/~/inspectorio/(/)?sms/"

    querystring-url-decode:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      rewrite:
        uri: "/sms/api/sms/$(uri_captures[2])?$(uri_captures[4])"
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
          - OPTIONS
        strip_path: true
        regex_priority: 100
      proxy:
        path: "/"
      paths:
        - "/~/inspectorio/(/)?sms/(.*)(%3F)(.*)"

    wechat:
      enabled: true
      appservice: main
      ingressClass: api
      allow_list: [10.0.0.0/8]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        strip_path: true
        regex_priority: 150
      paths:
        - "/~/inspectorio/sms/(integration/wechat/orgs$)"

    auth-router-root-sms:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        allow:
          enabled: true
          plugin: internal-ip-restriction
          config:
            allow:
              - 10.0.0.0/8
            message: Forbidden - You don't have permission to access this resource
            status: 403
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        protocols:
          - http
        regex_priority: 100
      paths:
        - "/"
      tlsDisabled: true
      hosts:
        - domains: # no using https
            - auth-router.default.svc.cluster.local

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: true
      internal:
        rules:
          high-5xx-rate:
            enabled: true
    ingress:
      kong-sso-sight-frontend:
        enabled: True
        gateways:
          - "mesh" # apply for Istio Mesh Sidecar only
        exportTo:
          - "kong" # exportTo Kong Gateway sidecar only
        hosts:
          - sms-default-main.sms.svc.cluster.local
          - sms-default-main.sms.svc
          - sms-default-main.sms
        customHttpRoutes:
          pre: #insert before http.*
            - match:
                - headers:
                    x-forwarded-host:
                      exact: api.inspectorio.com
                    x-forwarded-path:
                      exact: /inspectorio/sms/security/v1/saml2/user/check
                  ignoreUriCase: true
                  uri:
                    prefix: /sms/api/sms/security/v1/saml2/user/check
              headers:
                request:
                  set:
                    X-Forwarded-Host: kong-kube-prod.inspectorio.com
              route:
                - destination:
                    host: "sms-default-main" # match with dedicated service created by kongingress/proxy enabled
                    port:
                      number: 80
        http:
          - route:
              - destination:
                  host: "sms-default-main" # match with dedicated service created by kongingress/proxy enabled
                  port:
                    number: 80

celery-advanced-exporter:
  enabled: true
  environment:
    - name: CE_BROKER_URL
      value: redis://redis-redis-production.external-service.svc.cluster.local:6379/1
    - name: CE_RETRY_INTERVAL
      value: 5
