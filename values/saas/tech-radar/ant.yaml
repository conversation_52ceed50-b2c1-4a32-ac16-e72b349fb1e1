tech-radar:
  generic_chart_name: genapp-v1

  resources:
    limits:
      cpu: 0.3
      memory: 256Mi
    requests:
      cpu: 0.1
      memory: 128Mi

  probes:
    http:
      tech-radar-private-uptime-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - http://tech-radar-main.tech-radar.svc.cluster.local
            labels:
              class: saas-outage
              group: tech-radar
              domain_type: private
              domain_site: private

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: true
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress: {}
    egress: {}
