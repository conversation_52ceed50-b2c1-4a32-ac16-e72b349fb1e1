jimmy:
  kongingress:
    api:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      route:
        methods:
          - GET
          - PUT
          - POST
          - DELETE
          - PATCH
        strip_path: true
      paths:
        - "/scim/v2/"

  environment:
    JIMMY_PORT: '8080'
    JIMMY_SCIM_BASE_URL: ''
    HERMES_HOSTNAME: id.inspectorio.com
    RISE_HOSTNAME: rise-api.inspectorio.com
    RISE_USER_EMAIL: <EMAIL>
    RISE_TOKEN_TTL: '86400'
    RISE_ORGANIZATION_ID: 19ff8570-4e2c-4ff1-81ee-749075b7ced6
    SIGHT_HOSTNAME: api.inspectorio.com
    SIGHT_ORGANIZATION_ID: b76b4992-b5cd-42ca-aa5b-06a59b29a631
    SIGHT_USER_EMAIL: <EMAIL>
    SIGHT_TOKEN_TTL: '86400'
    ECOSYSTEM_AUTHENTICATION_METHOD: 'ins-saml'
    SYSTEM_ADMIN_ENABLED: 'true'
    TOKEN_EXPIRY_BUFFER_SECONDS: 600
    SENTRY_ENVIRONMENT: prd
  
  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps: []
      custom: []
    ingress:
      apps: []
