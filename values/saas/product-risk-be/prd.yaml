product-risk-be:
  generic_chart_name: genapp-v1

  appservices:
    main:
      enabled: true
      replicaCount: 3
      service:
        type: ClusterIP
        port: 80
        targetPort: main
        enabled: true
        protocol: TCP
        annotations: #Optional. additional annotations
          prometheus.io/saas-health-check: "true"
          prometheus.io/path: /health_check
      containers:
        main:
          ports:
            - name: main
              containerPort: 5000
              protocol: TCP
          resources:
            limits:
              cpu: 2
              memory: 1.2Gi
            requests:
              cpu: 100m
              memory: 768Mi
          livenessProbe:
            httpGet:
              path: /health_check
              port: main
            failureThreshold: 3
            periodSeconds: 5
          readinessProbe:
            httpGet:
              path: /health_check
              port: main
            failureThreshold: 3
            periodSeconds: 5
      kedaHPA:
        scalers:
          main:
            enabled: true
            minReplicaCount: 3
            maxReplicaCount: 12
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 75
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 20
                        periodSeconds: 90
                    selectPolicy: Max
            triggers:
              cpu:
                value: 80

  externalSecretStore:
    product-risk-be:
      source:
        data:
          - secretKey: JWT_SECRET_KEY
            remoteRef:
              key: product-risk-be-sms-jwt-secret-key
          - secretKey: SECRET_KEY
            remoteRef:
              key: product-risk-be-analytic3-secret-key
          - secretKey: SENTRY_DSN
            remoteRef:
              key: product-risk-be-sentry-default
          - secretKey: POSTGRES_ML_STORE_PASSWORD
            remoteRef:
              key: pg-ml-store-main
          - secretKey: OPENAI_API_KEY
            remoteRef:
              key: product-risk-be-open-api-key
          - secretKey: AZURE_API_KEY
            remoteRef:
              key: product-risk-be-azure_api_key
          - secretKey: AZURE_NLQ_API_KEY
            remoteRef:
              key: product-risk-be-azure-nlq-api-key
          - secretKey: POSTGRES_BI_OWNER_PASSWORD
            remoteRef:
              key: pg-analytic3-analytic2
          - secretKey: POSTGRES_ANALYTIC3_OWNER_PASSWORD
            remoteRef:
              key: pg-analytic3-main

  workers:
    celery:
      containers:
        celery:
          resources:
            limits:
              cpu: 1
              memory: 1Gi
            requests:
              memory: 128Mi
              cpu: 0.2

  probes:
    http:
      product-risk-be-health-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - http://product-risk-be-main.product-risk.svc.cluster.local/health_check
            labels:
              class: saas-outage
              group: product-risk
              domain_type: private
              domain_site: private

  environmentFrom:
    - secretRef:
        name: product-risk-be

  serviceAccount:
    create: true
    name: product-risk-be
    enableWorkloadIdentity: true

  jobs:
    migration:
      ttlSecondsAfterFinished: 60
      disabledGenerateName: true
      annotations:
        argocd.argoproj.io/hook: PreSync
        argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
        argocd.argoproj.io/sync-options: PrunePropagationPolicy=background

  environment:
    SERVICE: product-risk-be
    ENVIRONMENT: production
    DATABASE_URL: ************************************************************************************************************/ml-store?sslmode=require&sslcert=/tmp/dummycertpath
    REDIS_URL: redis://redis-ds-queues.external-service.svc.cluster.local:6379/2
    CACHE_TYPE: redis
    SMS_BASE_URL: http://auth-router.default.svc.cluster.local/sms/api/sms
    NOTIMANAGER_BASE_URL: http://notimanager-main.notimanager.svc.cluster.local/nms/v1
    FMS_BASE_URL: http://fms-main.fms.svc.cluster.local
    BI_DATABASE_URL: psql://analytic3:$(POSTGRES_BI_OWNER_PASSWORD)@pg-analytic3-analytic2.external-service.svc.cluster.local:5432/bi?sslmode=require&sslcert=/tmp/dummycertpath
    AN3_DATABASE_URL: "psql://analytic3:$(POSTGRES_ANALYTIC3_OWNER_PASSWORD)@pg-analytic3-main.external-service.svc.cluster.local:5432/analytic3?sslmode=require&sslcert=/tmp/dummycertpath"

  datadog:
    enabled: auto
    options:
      #https://docs.datadoghq.com/tracing/trace_collection/library_config/python/#pagetitle
      - name: DD_LOGS_INJECTION
        value: "false"
      - name: DD_TRACE_RATE_LIMIT
        value: 10
      - name: DD_TRACE_SAMPLE_RATE
        value: 1.0

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - DELETE
          - PUT
          - PATCH
        strip_path: true
      paths:
        - "/inspectorio/product-risk-prediction"

    default-options:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - OPTIONS
        strip_path: true
      paths:
        - "/inspectorio/product-risk-prediction"
        - "/inspectorio/caparecommender"

    default-pub-capa:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      customPlugins:
        rate-limiting:
          enabled: true
          plugin: rate-limiting
          config:
            second: 5
            minute: 100
            policy: local
      route:
        methods:
          - POST
          - GET
          - DELETE
          - PUT
          - PATCH
        strip_path: true
      paths:
        - "/inspectorio/caparecommender"
  
  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps:
      - notimanager:notimanager
      - fms:fms
      custom:
      - ports:
        - port: 5432
          protocol: TCP
        - port: 6379
          protocol: TCP
        - port: 11211
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
            except:
    ingress:
      apps:
      - sight-be:sight-be
      - tracking:tracking

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true
