defect-recommend-be:
  generic_chart_name: genapp-v1

  resources:
    limits:
      cpu: 1.5
      memory: 2Gi
    requests:
      cpu: 0.1
      memory: 1.2Gi

  environment:
    SMS_ACCOUNT_USER: "<EMAIL>"
    DATALAKE_DB_URL: "***********************************************************************************************************************/datalake?sslmode=require&sslcert=/tmp/dummycertpath"
    ML_STORE_DB_URL: "************************************************************************************************************/ml-store?sslmode=require&sslcert=/tmp/dummycertpath"
    MODEL_PATH_URL: "gs://inspectorio-ds-datalake-prod/ml_engine/defect_recommend/model_v3_1208.h5"
    MISSING_DATA_PATH_URL: "gs://inspectorio-ds-datalake-prod/ml_engine/defect_recommend/missing_data_v3_1208.pkl"
    MAX_LEN_DIC_PATH_URL: "gs://inspectorio-ds-datalake-prod/ml_engine/defect_recommend/max_len_dict_v3_1208.pkl"
    SPARSE_FEATURE_PATH_URL: "gs://inspectorio-ds-datalake-prod/ml_engine/defect_recommend/sparse_feature_encoders_v3_1208.pkl"

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress:
      apps:
        - "sight-be:sight-be"
        - "tracking:tracking"
    egress:
      apps: []
      custom:
        - to:
          - ipBlock:
              cidr: 10.0.0.0/8
          ports:
          - protocol: TCP
            port: 5432

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        strip_path: true
      paths:
        - "/inspectorio/defect-recommend/"

    options:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - OPTIONS
        strip_path: true
      paths:
        - "/inspectorio/defect-recommend/"
