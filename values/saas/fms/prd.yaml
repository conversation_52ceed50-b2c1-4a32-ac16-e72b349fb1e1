fms:
  generic_chart_name: genapp-v1

  appservices:
    main:
      replicaCount: 4
      containers:
        main:
          resources:
            requests:
              memory: 384Mi
            limits:
              memory: 2Gi
              cpu: 2
          startupProbe:
            httpGet:
              path: /health_check
              port: main
            periodSeconds: 5
            failureThreshold: 14
          livenessProbe:
            httpGet:
              path: /health_check
              port: main
            initialDelaySeconds: 15
            periodSeconds: 20
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /health_check
              port: main
            initialDelaySeconds: 15
            periodSeconds: 20
            timeoutSeconds: 5
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload='fms-appservice-main'})"
          interval: 15s # call expr each internval
          name: fms_rps_total # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 25
        scalers:
          main:
            enabled: true
            target: fms-appservice-main
            minReplicaCount: 4
            maxReplicaCount: 16
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 50
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 90
                    selectPolicy: Max

    files-gcloud-storage:
      enabled: true
      service:
        enabled: true
        type: ExternalName
        externalName: inspectorio-files-upload-production.storage.googleapis.com
        port: 80
        protocol: TCP

  workers:
    default:
      replicaCount: 2
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(rate(dramatiq_messages_total{queue_name='crop_image'}[2m]))/sum(kube_deployment_spec_replicas{deployment='fms-worker-default'})"
          interval: 30s # call expr each internval
          name: queue_messages_crop_image # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 0.83
        scalers:
          worker:
            enabled: true
            target: fms-worker-default
            minReplicaCount: 2
            maxReplicaCount: 12
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 50
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 90
                    selectPolicy: Max
            triggers:
              cpu:
                value: "100"
              memory:
                value: "800"

  probes:
    http:
      fms-private-uptime-check:
        module: saas_healthcheck
        scrapeTimeout: 30s
        targets:
          staticConfig:
            static:
              - http://fms-main.fms.svc.cluster.local/health_check
            labels:
              class: saas-outage
              group: fms
              domain_type: private
              domain_site: private

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  datadog:
    enabled: auto
    options:
      #https://docs.datadoghq.com/tracing/trace_collection/library_config/python/#pagetitle
      - name: DD_LOGS_INJECTION
        value: "false"
      - name: DD_TRACE_RATE_LIMIT
        value: 10
      - name: DD_TRACE_SAMPLE_RATE
        value: 1.0

  environment:
    FILES_URL: https://files.inspectorio.com/
    FILES_CDN_URL: https://files.inspectorio-platform.com/
    GCS_BUCKET: inspectorio-files-upload-production
    SIGNED_URL_EXPIRATION_SECONDS: 86400
    NGINX_MAX_ALLOWED_BODY_SIZE: 400
    NGINX_WORKER: 4
    SMS_USER_ORGANIZATION_ACCESS_CHECK_ENDPOINT: http://auth-router.default.svc.cluster.local/sms/api/sms/v1/{organization}/user/access
    SMS_USER_ORGANIZATION_ACCESS_CHECK_DISABLED: 1
    ALLOWED_AUTHENTICATED_SERVICES: integration-api,quality-control-be,sms
    FILES_SIGNATURE_REQUEST_LIMIT: 1000
    DRAMATIQ_BROKER_URL: redis://redis-core-queues.external-service.svc.cluster.local:6379/6
    CROP_IMAGE_QUEUE: "crop_image"
    MONITORING_ENABLED: True
    dramatiq_restart_delay: "30000"
    ENABLED_SIGN_PATH_LOGGING: True
    prometheus_multiproc_dir: "/app"
    FILE_UPLOAD_SESSION_EXPIRATION_SECONDS: 600
    FILE_UPLOAD_SESSION_URL: "https://files-integration.inspectorio.com/signed-upload/"
    REDIS_URI: "redis://redis-core-queues.external-service.svc.cluster.local:6379/6"
    GOOGLE_APPLICATION_CREDENTIALS: "/app/gcp-serviceaccount/serviceaccount.json"
    CELERY_BROKER_URL: "redis://redis-core-queues.external-service.svc.cluster.local:6379/6"
    CELERY_RESULT_BACKEND: "redis://redis-core-queues.external-service.svc.cluster.local:6379/6"

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress:
      apps: # <namespace>:<app-name>
      - integration-api:integration-api
      - analytic3:analytic3
      - car:car
      - passport:passport-be
      - tracking:tracking
      - mobileresponder:mobileresponder
      - sight-be:sight-be
      - master-data:master-data
      - product-risk:product-risk-be
      - sms:sms
      - report-html:report-html
    egress:
      apps: []
      custom:
      - ports:
        - port: 6379
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
      - ports:
        - port: 27017
          protocol: TCP
        to:
        - ipBlock:
            cidr: 0.0.0.0/0

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: [bouncer-auth-disabled]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
          - PATCH
        strip_path: true
      paths: ["/inspectorio/fms/v3/"]
      proxy:
        path: "/v3/"

    restriction:
      enabled: true
      appservice: main
      ingressClass: api
      allow_list: [10.0.0.0/8]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
          - PATCH
        strip_path: true
      paths:
        [
          "/~/fms/v3/file-upload/*",
          "/~/inspectorio/fms/v3/file-upload/*",
          "/~/inspectorio/fms/v3/internal/*"
        ]

    options:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - OPTIONS
        strip_path: true
      paths: ["/inspectorio/fms/v3/"]

    files-integrations:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: []
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
          - PATCH
          - OPTIONS
        strip_path: false
      paths:
        [
          "/signed-upload/",
          "/swagger/"
        ]
      proxy:
        path: "/"
      hosts:
        - domains:
            - "files-integration.inspectorio.com"
          tlsSecretName: inspectorio-com-common-tls

    fms-inditex: # new kong ingress api
      enabled: true
      appservice: files-gcloud-storage
      ingressClass: api
      plugins: ["fms-auth"]
      route:
        methods:
          - POST
          - PUT
          - GET
          - DELETE
          - PATCH
          - OPTIONS
        strip_path: true
      proxy:
        path: "/"
      paths:
        - "/"
      upstream:
        host_header: "inspectorio-files-upload-production.storage.googleapis.com"
      hosts:
        - domains:
            - "fms-gcp.inspectorio-platform.com"
          tlsSecretName: inspectorio-com-common-tls

celery-advanced-exporter:
  enabled: true
  environment:
    - name: CE_BROKER_URL
      value: redis://redis-core-queues.external-service.svc.cluster.local:6379/6
    - name: CE_RETRY_INTERVAL
      value: 5
