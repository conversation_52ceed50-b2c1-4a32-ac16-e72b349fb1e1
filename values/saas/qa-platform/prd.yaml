qa-platform:
  generic_chart_name: genapp-v1

  resources:
    limits:
      cpu: 0.5
      memory: 1Gi
    requests:
      cpu: 0.1
      memory: 512Mi

  environment:
    POSTGRES_HOST: "pg-qa-platform-main.external-service.svc.cluster.local"
    POSTGRES_PORT: "5432"
    POSTGRES_USERNAME: "qa-platform"
    POSTGRES_DBNAME: "qa-platform"
    ES_HOST: "http://eck-sight-monitoring-es-http.eck-sight.svc.cluster.local:9200"
    ES_INDEX: "qa-platform"
    GCS_BUCKET_NAME: "inspectorio-files-upload-production"
    GCS_BUCKET_PATH: "issue_solution"

  istio:
    enabled: true
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - DELETE
        strip_path: true
      paths:
        - "/inspectorio/is-platform/"
    options:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - OPTIONS
        strip_path: true
      paths:
        - "/inspectorio/is-platform/"
  
  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps:
      - eck-sight:eck-sight-monitoring
      custom:
      - ports:
        - port: 5432
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
    ingress:
      apps: []