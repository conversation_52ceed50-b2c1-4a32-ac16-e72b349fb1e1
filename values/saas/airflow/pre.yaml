airflow:
  environment:
    AIRFLOW__LOGGING__REMOTE_BASE_LOG_FOLDER: gs://inspectorio-preprod-pre-airflow-logs
    AIRFLOW__WEBSERVER__BASE_URL: https://airflow.pre.inspectorio.com
    SENDGRID_MAIL_FROM: <EMAIL>
    BI_DB_POSTGRES_URL: **************************************************************************************************************************/bi?sslmode=require&sslcert=/tmp/dummycertpath
    BI_POSTGRES_URL: **************************************************************************************************************************/analytic2?sslmode=require&sslcert=/tmp/dummycertpath
    DATALAKE_POSTGRES_URL: "******************************************************************************************************************/datalake?sslmode=require&sslcert=/tmp/dummycertpath"
    DATALAKE_RO_POSTGRES_URL: "******************************************************************************************************************/datalake" # should be removed
    KAFKA_CONNECT_REST_URL: http://kafka-connect-cdc-main-kafka-connect-api.kafka-connect-cdc.svc.cluster.local:8083
    KAFKA_CONNECT_REST_CDC_URL: http://kafka-connect-cdc-main-kafka-connect-api.kafka-connect-cdc.svc.cluster.local:8083
    KAFKA_SCHEMA_REGISTRY_REST_URL: http://datalake-kafka-v2-gcp-pre-cp-schema-registry.default.svc.cluster.local:8081
    ML_STORE_POSTGRES_URL: "************************************************************************************************************/ml-store?sslmode=require&sslcert=/tmp/dummycertpath"
    MONITORING_URL: http://sight-be-main.sight-be.svc.cluster.local/inspectorio/monitoring/v1
    QC_BE_POSTGRES_URL: ********************************************************************************************************/sight-be
    SMS_POSTGRES_URL: "**************************************************************************************************************/passport-be?sslmode=require&sslcert=/tmp/dummycertpath"
    SUPERSET_OLAP_POSTGRES_URL: "*********************************************************************************************************************/superset?sslmode=require&sslcert=/tmp/dummycertpath"
    IMS_URL: http://sight-be-main.sight-be.svc.cluster.local/inspectorio/ims/v1
    QCB_URL: http://sight-be-main.sight-be.svc.cluster.local
    MASTER_DATA_POSTGRES_URL: "*****************************************************************************************************************/master-data?sslmode=require&sslcert=/tmp/dummycertpath"
    ORG_MAPPING_ISSUES_SLACK_CHANNEL: org_mapping_issues
    ENABLE_ORG_MAPPING_ISSUES_SLACK_MSG: true
    DOCUMENT_VALIDATOR_URL: http://document-validator-main.document-validator.svc.cluster.local

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress:
      apps: []
    egress:
      apps:
        - "document-validator:document-validator"
        - "factory-risk-be:factory-risk-be"
        - "product-risk:product-risk-be"
        - "sight-be:sight-be"
        - "timing-formula:timing-formula"
      custom:
        - ports:
          - port: 5432
            protocol: TCP
          - port: 6379
            protocol: TCP
          to:
          - ipBlock:
              cidr: 10.0.0.0/8
        - ports:
          - port: 27017
            protocol: TCP
          to:
          - ipBlock:
              cidr: 0.0.0.0/0
        - ports:
          - port: 22 # For gitlab
            protocol: TCP
          to:
          - ipBlock:
              cidr: ************/32 # common-nginx.ant.inspectorio.com.
        - ports:
          - port: 443 # For https://airflow.apache.org/docs/apache-airflow-providers-cncf-kubernetes/stable/kubernetes_executor.html
            protocol: TCP
          to:
          - ipBlock:
              cidr: **********/28
        - ports:
          - port: 9092
            protocol: TCP
          to:
          - namespaceSelector:
              matchLabels:
                name: kafka
            podSelector:
              matchLabels:
                app.kubernetes.io/instance: kafka-main
                app.kubernetes.io/name: kafka
        - ports:
          - port: 8083
            protocol: TCP
          to:
          - namespaceSelector:
              matchLabels:
                name: kafka-connect-cdc
            podSelector:
              matchLabels:
                app.kubernetes.io/instance: kafka-connect-cdc-main-kafka
                app.kubernetes.io/name: kafka-connect
        - ports:
          - port: 8123
            protocol: TCP
          to:
          - namespaceSelector:
              matchLabels:
                name: clickhouse-ds
            podSelector:
              matchLabels:
                clickhouse.altinity.com/chi: clickhouse-ds-clickhouse-main
                clickhouse.altinity.com/namespace: clickhouse-ds
        - ports:
          - port: 5432
            protocol: TCP
          to:
          - namespaceSelector:
              matchLabels:
                name: pgbouncer
            podSelector:
              matchLabels:
                app.kubernetes.io/instance: pgbouncer-sight-be-main
                app.kubernetes.io/name: pgbouncer
        - ports:
          - port: 9200
            protocol: TCP
          to:
          - namespaceSelector:
              matchLabels:
                name: eck-infra
            podSelector:
              matchLabels:
                elasticsearch.k8s.elastic.co/cluster-name: eck-infra-logs

  externalSecretStore:
    airflow:
      source:
        data:
          - secretKey: AIRFLOW__CORE__FERNET_KEY # name of key in k8s secret
            remoteRef:
              key: airflow-fernet_key
          - secretKey: FERNET_KEY # will be removed
            remoteRef:
              key: airflow-fernet_key
          - secretKey: SENTRY_DSN
            remoteRef:
              key: airflow-sentry-default
          - secretKey: SENDGRID_API_KEY
            remoteRef:
              key: airflow-sendgrid_api_key
          - secretKey: GITLAB_JOB_TOKEN
            remoteRef:
              key: airflow-gitlab_job_token
          - secretKey: HUBSPOT_API_KEY
            remoteRef:
              key: airflow-hubspot_api_key
          - secretKey: CHURN_ZERO_API_KEY
            remoteRef:
              key: airflow-churn_zero_api_key
          - secretKey: POSTGRES_ML_STORE_PASSWORD
            remoteRef:
              key: pg-ml-store-main
          - secretKey: POSTGRES_PASSPORT_PASSWORD
            remoteRef:
              key: pg-passport-be-main-airflow
          - secretKey: POSTGRES_SIGHT_PASSWORD
            remoteRef:
              key: pg-sight-be-main-airflow
          - secretKey: POSTGRES_ANALYTIC2_OWNER_PASSWORD
            remoteRef:
              key: pg-analytic3-analytic2
          - secretKey: POSTGRES_DATALAKE_OWNER_PASSWORD
            remoteRef:
              key: pg-datalake-main
          - secretKey: POSTGRES_ANALYTIC3_OWNER_PASSWORD
            remoteRef:
              key: pg-analytic3-main
          - secretKey: POSTGRES_MASTER_DATA_PASSWORD
            remoteRef:
              key: pg-master-data-main-airflow
          - secretKey: POSTGRES_AIRFLOW_OWNER_PASSWORD
            remoteRef:
              key: pg-airflow-main
          - secretKey: CAPA_MONGODB_URL
            remoteRef:
              key: mongodb-atlas-ds-car
              property: uri
          - secretKey: POSTGRES_RISE_OWNER_PASSWORD
            remoteRef:
              key: pg-rs-backend-main
          - secretKey: POSTGRES_SUPERSET_PASSWORD
            remoteRef:
              key: pg-analytics-superset-main-airflow
          - secretKey: OPERATION_NS_API_BASE_URL
            remoteRef:
              key: airflow-netsuite
              property: endpoint
          - secretKey: OPERATION_NS_ACCOUNT
            remoteRef:
              key: airflow-netsuite
              property: account
          - secretKey: OPERATION_NS_CONSUMER_KEY
            remoteRef:
              key: airflow-netsuite
              property: consumer_key
          - secretKey: OPERATION_NS_CONSUMER_SECRET
            remoteRef:
              key: airflow-netsuite
              property: consumer_secret
          - secretKey: OPERATION_NS_TOKEN_ID
            remoteRef:
              key: airflow-netsuite
              property: token_id
          - secretKey: OPERATION_NS_TOKEN_SECRET
            remoteRef:
              key: airflow-netsuite
              property: token_secret
          - secretKey: CLICKHOUSE_AIRFLOW_PASSWORD
            remoteRef:
              key: clickhouse-ds-clickhouse-airflow
          - secretKey: DOCUMENT_VALIDATOR_API_TOKEN
            remoteRef:
              key: airflow-document-validator-api-token

    eck-infra-logs-airflow-credential:
      template:
        engineVersion: v2
        data:
          ECK_INFRA_USERNAME: "{{ .username }}"
          ECK_INFRA_PASSWORD: "{{ .password }}"
      source:
        dataFrom:
          - extract:
              key: eck-infra-airflow
              conversionStrategy: Default
              decodingStrategy: None

  environmentFrom:
    - secretRef:
        name: airflow
    - secretRef:
        name: eck-infra-logs-airflow-credential

  appservices:
    web:
      containers:
        web:
          resources:
            limits:
              cpu: 500m
              memory: 3Gi
            requests:
              cpu: 100m
              memory: 640Mi

    worker:
      containers:
        worker:
          resources:
            limits:
              cpu: 4
              memory: 4.5Gi
            requests:
              cpu: 500m
              memory: 3Gi

    statsd-exporter:
      monitoring:
        podMonitor:
          enabled: true
          endpoints:
            - path: /metrics
              port: metrics
              interval: 30s
              metricRelabelings:
                - sourceLabels: [__name__]
                  action: drop
                  regex: "airflow_local_task_job_task_exit"

  workers:
    scheduler:
      containers:
        scheduler:
          resources:
            limits:
              cpu: 4
              memory: 2Gi
            requests:
              cpu: 1
              memory: 1Gi

  istio:
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: true
      internal:
        rules:
          high-5xx-rate:
            enabled: true
    ingress:
      default:
        enabled: true
        hosts:
          - airflow.pre.inspectorio.com
        http:
          - match:
              - headers:
                  traffic-class:
                    exact: inspectorio
                ignoreUriCase: true
                uri:
                  prefix: /login
            route:
              - destination:
                  appservice: web
          - match:
              - ignoreUriCase: true
                uri:
                  prefix: /login
              - ignoreUriCase: true
                uri:
                  prefix: /.pomerium
            route:
              - destination:
                  host: pomerium-proxy.pomerium.svc.cluster.local
                  port:
                    number: 80
                headers:
                  request:
                    set:
                      Host: airflow.pre.inspectorio.com
                      X-Forwarded-Proto: https
          - match:
              - uri:
                  prefix: /
                ignoreUriCase: true
            route:
              - destination:
                  appservice: web
        denyRules:
          deny:
            when:
              - key: request.headers[Path-Insensitive]
                values:
                  - "/api/v1/ui*"

  nodeSelector:
    data-platform: "true"
  tolerations:
    - key: "pool-type"
      operator: "Equal"
      value: "data-platform"
      effect: "NoSchedule"

  alertrules:
    prometheusName: prometheus-main
    interval: 1m
    rules:
      MaxTaskRemain:
        runbook_url: https://grafana.inspectorio.com/d/f0c44b04-02df-4a4e-8420-c1f2211b0635/airflow?from=now-1h&orgId=1&timezone=utc&to=now&var-datasource=P37195EA69EE965FF&var-service=airflow&viewPanel=panel-29
        summary: Airflow max remaining task is low - less than 5 in last 10 minutes. Please check the schedulers!
        severity:
          warning:
            expr: max_over_time(airflow_executor_open_slots[1h]) <= 5
            for: 10m
