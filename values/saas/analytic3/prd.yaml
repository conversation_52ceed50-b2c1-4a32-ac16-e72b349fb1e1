analytic3:
  environment:
    U<PERSON><PERSON>_HTTP_TIMEOUT: 300
    INSPECTION_REPORT_URL_TEMPLATE: https://app.inspectorio.com/inspections/report-html-internal/{}
    SEND_GRID_FROM_EMAIL: <EMAIL>
    SEND_GRID_USERS_LIST: 7808137
    RISE_REDIS_URI: redis://redis-redis-rs-production.external-service.svc.cluster.local:6379/0
    REDIS_URI: redis://redis-ds-queues.external-service.svc.cluster.local:6379/1
    DS_REDIS_URI: redis://redis-ds-queues.external-service.svc.cluster.local:6379/1
    CELERY_BROKER_URL: redis://redis-ds-queues.external-service.svc.cluster.local:6379/1
    SITE_DOMAIN: https://app.inspectorio.com
    DB_STATEMENT_TIMEOUT: 900000
    SKIP_WEB_HOOK: true
    CACHE_ENABLE: true
    CACHE_MIDDLEWARE_KEY_PREFIX: analytic3_cache
    CACHE_AUTH_KEYS: org_id
    CACHE_RESPONSE_LIMIT: 0
    CACHE_REFRESH_TIME: 7200
    CACHE_LIVE_TIME: 43200
    CACHE_SHORT_PATH1: .*/performance/(general_purchase_orders|factories_details|bookings|assignments)/.*\\?date_range=.*&date_range=.*
    CACHE_REGION_EXPIRATION_TIME: 3600
    EXTENDED_RISE_PERMISSIONS: '{"6d10cda6-fcf1-4be6-bb4e-40527d746a99":{"permissions":["ANALYTIC_VIEW_ADVANCE"],"expire_on":"2021-07-01"},"763c36c7-b33e-468b-aafd-29c18cb65c89":{"permissions":["ANALYTIC_VIEW_ADVANCE"],"expire_on":"2021-06-15"}}'
    DEMO_USER_IDS: "33571,30393,11229,7940"
    DOCUMENT_VALIDATOR_BASE_URL: "http://document-validator-main.document-validator.svc.cluster.local"

  externalSecretStore:
    main:
      generateSecretName: true
      source:
        data:
          - secretKey: POSTGRES_ANALYTIC3_OWNER_PASSWORD
            remoteRef:
              key: pg-analytic3-main
          - secretKey: POSTGRES_BI_OWNER_PASSWORD
            remoteRef:
              key: pg-analytic3-analytic2
          - secretKey: SENTRY_DSN
            remoteRef:
              key: analytic3-sentry-default
          - secretKey: SECRET_KEY
            remoteRef:
              key: analytic3-secret-key
          - secretKey: GOOGLE_API_KEY
            remoteRef:
              key: analytic3-google-api-key
          - secretKey: SEND_GRID_API_KEY
            remoteRef:
              key: analytic3-sendgrid-api-key
          - secretKey: BOUNCER_SECRET_KEY
            remoteRef:
              key: analytic3-bouncer-secret-key
          - secretKey: SMS_PASSWORD
            remoteRef:
              key: analytic3-sms-password
          - secretKey: OPENAI_API_KEY
            remoteRef:
              key: analytic3-open-api-key
          - secretKey: CLICKHOUSE_ANALYTIC3_PASSWORD
            remoteRef:
              key: clickhouse-ds-clickhouse-analytic3

  appservices:
    main:
      containers:
        main:
          resources:
            limits:
              cpu: 2
              memory: 8Gi
            requests:
              cpu: 0.2
              memory: 3Gi

  workers:
    beat:
      containers:
        beat:
          resources:
            limits:
              cpu: 1
              memory: 512Mi
            requests:
              cpu: 0.1
              memory: 160Mi
    celery:
      containers:
        celery:
          resources:
            limits:
              cpu: 1.5
              memory: 6Gi
            requests:
              cpu: 0.5
              memory: 4Gi

  jobs:
    migration:
      ttlSecondsAfterFinished: 60
      disabledGenerateName: true
      annotations:
        argocd.argoproj.io/hook: PreSync
        argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
        argocd.argoproj.io/sync-options: PrunePropagationPolicy=background

  istio:
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    egress:
      apps:
        - fms:fms
        - rs:rs-backend
        - document-validator:document-validator
        - timing-formula:timing-formula
        - sight-be:sight-be
        - factory-risk-be:factory-risk-be
        - notimanager:notimanager
      custom:
        - ports:
          - port: 5432
            protocol: TCP
          - port: 6379
            protocol: TCP
          - protocol: TCP
            port: 11211
          to:
          - ipBlock:
              cidr: 10.0.0.0/8
        - ports:
          - port: 8123
            protocol: TCP
          to:
          - namespaceSelector:
              matchLabels:
                name: clickhouse-ds
            podSelector:
              matchLabels:
                clickhouse.altinity.com/chi: clickhouse-ds-clickhouse-main
                clickhouse.altinity.com/namespace: clickhouse-ds
        - ports:
          - port: 22 # For gitlab
            protocol: TCP
          to:
          - ipBlock:
              cidr: ************/32 # common-nginx.ant.inspectorio.com.
    ingress:
      apps:
        - tracking:tracking
        - sight-be:sight-be
        - integration-api:integration-api

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - PUT
          - PATCH
          - DELETE
        strip_path: true
      paths:
        - /inspectorio/analytic3/

    options:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - OPTIONS
        strip_path: true
      paths:
        - /inspectorio/analytic3/

    deny:
      enabled: true
      appservice: main
      ingressClass: api
      allow_list: [10.0.0.0/8]
      route:
        methods:
          - POST
          - GET
          - PUT
          - PATCH
          - DELETE
          - OPTIONS
        strip_path: true
      paths:
        - /inspectorio/analytic3/static/
        - /inspectorio/analytic3/admin/

  nodeSelector:
    data-platform: "true"
  tolerations:
    - key: "pool-type"
      operator: "Equal"
      value: "data-platform"
      effect: "NoSchedule"

  alertrules:
    interval: 1m
    rules:
      CeleryTaskHighFailRate:
        description: More than 5% tasks failed for the task {{ $labels.name }} the past 10m.
        runbook_url: https://inspectoriodocs.atlassian.net/wiki/x/tICj3
        summary: Celery high task fail rate {{ $labels.group }}.
        severity:
          warning:
            expr: &celery-task-high-fail-rate |-
              (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="analytic3",name!~"None",queue_name!~"None"}[10m])) / (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="analytic3",name!~"None",queue_name!~"None"}[10m]))+sum by (group, queue_name, name) (increase(celery_task_succeeded_total{group="analytic3",name!~"None",queue_name!~"None"}[10m])))*100) > 5
            for: 10m
          error:
            expr: *celery-task-high-fail-rate
            for: 25m
          critical:
            expr: *celery-task-high-fail-rate
            for: 40m
            labels:
              class: saas-outage
