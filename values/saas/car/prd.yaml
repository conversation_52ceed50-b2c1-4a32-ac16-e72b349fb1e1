car:
  appservices:
    main:
      containers:
        main:
          resources:
            limits:
              cpu: 0.5
              memory: 2048Mi
            requests:
              cpu: 100m
              memory: 300Mi
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "sum(istio_requests_total:rate1m{destination_workload='car-appservice-main'}) / sum(kube_deployment_status_replicas{deployment='car-appservice-main'})"
          interval: 10s # call expr each internval
          name: car_per_pod_rps # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
        scalers:
          main:
            enabled: true
            minReplicaCount: 2
            maxReplicaCount: 12
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior:
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 100
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 20
                        periodSeconds: 150
                    selectPolicy: Max
            triggers:
                cpu:
                  value: "80"

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress:
      apps:
      - tracking:tracking
      - sight-be:sight-be
      - integration-api:integration-api
    egress:
      apps:
      - fms:fms
      - sight-be:sight-be
      - notimanager:notimanager
      custom:
      - ports:
        - port: 6379
          protocol: TCP
        to:
        - ipBlock:
            cidr: 10.0.0.0/8
      - ports:
        - port: 27017 #mongodb atlas
          protocol: TCP
        to:
        - ipBlock:
            cidr: 0.0.0.0/0
      - ports:
        - port: 9092
          protocol: TCP
        to:
        - namespaceSelector:
            matchLabels:
              name: kafka
          podSelector:
            matchLabels:
              app.kubernetes.io/instance: kafka-main
              app.kubernetes.io/name: kafka

  workers:
    default:
      enabled: true
      kedaHPA:
        prometheus: # Prometheus metric based autoscaling
          expr: "round(sum(celery_queue_length{exported_namespace='car'})/((count(count by(hostname)(idelta(celery_worker_up_timestamp{exported_namespace='car'}[1m])>0))*10)*1-(sum(rate(celery_task_runtime_bucket{le='+Inf', exported_namespace='car'}[1m]))-sum(rate(celery_task_runtime_bucket{le='2.5', exported_namespace='car'}[1m])))or vector(1)))"
          interval: 10s # call expr each internval
          name: car_workers_hpa_metric # generic app will create synthetic metric `name` with PrometheusRule
          threshold: 2
        scalers:
          worker:
            enabled: true
            minReplicaCount: 1
            maxReplicaCount: 15
      
  environment:
    FMS_URL: http://fms-main.fms.svc.cluster.local/v3/{org_id}/files
    AMS_BASE_URL: http://sight-be-main.sight-be.svc.cluster.local/inspectorio/ams/v1
    NMS_BASE_URL: http://notimanager-gcp-prd-notimanager.default.svc.cluster.local/notimanager
    FRONTEND_URL: https://app.inspectorio.com
    ASSIGNMENTS_BASE_URL: http://sight-be-main.sight-be.svc.cluster.local/inspectorio/ams/v1
    INSPECTIONS_BASE_URL: http://sight-be-main.sight-be.svc.cluster.local/inspectorio/ims/v1
    NOTIMANAGER_BASE_URL: http://notimanager-main.notimanager.svc.cluster.local/nms/v1
    CAR_BASE_URL: http://car-main/v1
    QUALITY_CONTROL_BE_URL: http://sight-be-main.sight-be.svc.cluster.local
    BMS_BASE_URL: http://sight-be-main.sight-be.svc.cluster.local/inspectorio/bms
    BOOKING_BASE_URL: http://sight-be-main.sight-be.svc.cluster.local/inspectorio/bms/v1
    SMS_USER_NAME: <EMAIL>
    KAFKA_URL: kafka-main-kafka-bootstrap.kafka.svc.cluster.local:9092
    SYSTEM_UPDATED_AT_TOPIC: car.system_updated_at
    IS_OUTBOX_CAPA_EVENT_PRODUCER_ENABLED: 1

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  istio:
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true

  kongingress:
    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - PUT
          - DELETE
          - GET
        strip_path: true
      paths:
        - "/inspectorio/car/"

    options:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - OPTIONS
        strip_path: true
      paths:
        - "/inspectorio/car/"

  infra:
    services:
      kafkacluster:
        main:
          topics:
            system_updated_at:
              config: # All options https://docs.confluent.io/platform/current/installation/configuration/topic-configs.html#message-format-version
                retention.ms: 604800000 # 7 Days

  alertrules:
    interval: 1m
    rules:
      CeleryTaskHighFailRate:
        description: More than 5% tasks failed for the task {{ $labels.name }} the past 10m.
        runbook_url: https://inspectoriodocs.atlassian.net/wiki/x/tICj3
        summary: Celery high task fail rate {{ $labels.group }}.
        severity:
          warning:
            expr: &celery-task-high-fail-rate |-
              (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="car",name!~"None",queue_name!~"None"}[10m])) / (sum by (group, queue_name, name) (increase(celery_task_failed_total{group="car",name!~"None",queue_name!~"None"}[10m]))+sum by (group, queue_name, name) (increase(celery_task_succeeded_total{group="car",name!~"None",queue_name!~"None"}[10m])))*100) > 5
            for: 10m
          error:
            expr: *celery-task-high-fail-rate
            for: 25m
          critical:
            expr: *celery-task-high-fail-rate
            for: 40m
            labels:
              class: saas-outage

celery-advanced-exporter:
  enabled: true
  environment:
    - name: CE_BROKER_URL
      value: redis://redis-sight-queues.external-service.svc.cluster.local:6379/1
    - name: CE_RETRY_INTERVAL
      value: 5
