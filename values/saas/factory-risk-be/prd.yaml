factory-risk-be:
  appservices:
    main:
      containers:
        main:
          resources:
            limits:
              cpu: 3
              memory: 5Gi
            requests:
              cpu: 1
              memory: 2Gi
      replicaCount: 3
      kedaHPA:
        scalers:
          main:
            enabled: true
            minReplicaCount: 5
            maxReplicaCount: 10
            advanced:
              horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
                behavior: # Optional. Use to modify HPA's scaling behavior
                  scaleUp:
                    stabilizationWindowSeconds: 0
                    policies:
                      - type: Percent
                        value: 75
                        periodSeconds: 30
                    selectPolicy: Max
                  scaleDown:
                    stabilizationWindowSeconds: 300
                    policies:
                      - type: Percent
                        value: 10
                        periodSeconds: 120
                    selectPolicy: Max
            triggers:
              cpu:
                value: "100"

  environment:
    ML_STORE_POSTGRES_URL: ************************************************************************************************************/ml-store?sslmode=require&sslcert=/tmp/dummycertpath
    DATALAKE_POSTGRES_URL: *******************************************************************************************************************/datalake?sslmode=require&sslcert=/tmp/dummycertpath
    MODEL_PATH_URL: gs://inspectorio-ds-datalake-prod/ml_engine/factory_risk/fr_model_2020_02_26.pkl
    MODEL_PATH_URL_v2: gs://inspectorio-ds-datalake-prod/ml_engine/factory_risk/fr_model_2020_10_10_v2.pkl
    MODEL_PATH_URL_v3: gs://inspectorio-ds-datalake-prod/ml_engine/factory_risk/fr_model_v3_2021_03_01.pkl
    MODEL_PATH_URL_v4: gs://inspectorio-ds-datalake-prod/ml_engine/factory_risk/fr_model_6.1_aka_v4_2021_04_30.pkl
    MODEL_PATH_URL_v5: gs://inspectorio-ds-datalake-prod/ml_engine/factory_risk/fr_model_v5_mono_2021_05_21.pkl
    MODEL_PATH_URL_v81: gs://inspectorio-ds-datalake-prod/ml_engine/factory_risk/tgt_frp_v8_1_2021_10_19.pkl
    MODEL_PATH_URL_v82: gs://inspectorio-ds-datalake-prod/ml_engine/factory_risk/tgt_frp_v8_2_2021_10_19.pkl
    MODEL_PATH_URL_v83: gs://inspectorio-ds-datalake-prod/ml_engine/factory_risk/tgt_frp_v8_3_2021_10_19.pkl
    WM_MODEL_PATH_URL_v41: gs://inspectorio-ds-datalake-prod/ml_engine/factory_risk/wm_frp_model_v41__2022_07_25.pkl
    NOTIMANAGER_BASE_URL: http://notimanager-main.notimanager.svc.cluster.local/nms/v1
    FRP_NOTI_DARK_FEATURE: 289
    FRP_NOTI_REVIEW_ALERT_DARK_FEATURE: 532

  networkPolicy:
    enabled: true
    denyAll:
    - Ingress
    - Egress
    ingress:
      apps: # <namespace>:<app-name>
        - "airflow:airflow"
        - "analytic3:analytic3"
        - "tracking:tracking"
        - "sight-be:sight-be"
      custom: []
    egress:
      apps: # <namespace>:<app-name>
        - "notimanager:notimanager"
      custom:
        - to:
          - ipBlock:
              cidr: 10.0.0.0/8
          ports:
          - protocol: TCP
            port: 5432 # postgresql
          - protocol: TCP
            port: 6379 # redis

  jobs:
    migration:
      ttlSecondsAfterFinished: 60
      disabledGenerateName: true
      annotations:
        argocd.argoproj.io/hook: PreSync
        argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
        argocd.argoproj.io/sync-options: PrunePropagationPolicy=background

  kongalert:
    rules:
      high-5xx-rate:
        enabled: true

  kongingress:
    default:
      enabled: true
      appservice: main
      ingressClass: api
      plugins: ["bouncer-auth-default"]
      route:
        methods:
          - POST
          - GET
          - DELETE
          - PUT
          - PATCH
        strip_path: true
      paths:
        - "/inspectorio/factory-risk-prediction"

    options:
      enabled: true
      appservice: main
      ingressClass: api
      route:
        methods:
          - OPTIONS
        strip_path: true
      paths:
        - "/inspectorio/factory-risk-prediction"

  istio:
    alerts:
      public:
        rules:
          high-5xx-rate:
            enabled: false
      internal:
        rules:
          high-5xx-rate:
            enabled: true
