argo-cd:
  configs:
    cm:
      url: "https://argocd.gcp-ant.inspectorio.com/"
      accounts.devops-bot: api<PERSON><PERSON>, login
      oidc.config: |
        name: Azure
        issuer: https://login.microsoftonline.com/c483e930-d691-49ab-98df-e6c3b83b633b/v2.0
        clientID: a6514bd0-aa73-4038-9ae9-c9d3e9015ff5
        clientSecret: $argocd-azure-app-oidc:oidc.azuread.clientSecret
        requestedIDTokenClaims:
          groups:
            essential: true
        requestedScopes:
          - openid
          - profile
          - email
    rbac:
      ########################################################################################
      # ArgoCD - Infrastructure team - RBAC Management
      ########################################################################################
      # 5a50e7c4-889f-4cba-b2a6-45582721dd87 : AzureGroup DevOps (old)
      # 4e1c971f-3c03-444e-b1d3-aa1c7259f9d4 : AzureGroup insp-infra (new)
      # Notes:
      # - Even infra team, still have some restrictions to avoid breaking system
      # - devops-bot : For CICD Gitops pipeline purpose only
      policy.infra-roles.csv: |
        g, "5a50e7c4-889f-4cba-b2a6-45582721dd87", role:Infra_Main_RBAC
        g, "4e1c971f-3c03-444e-b1d3-aa1c7259f9d4", role:Infra_Main_RBAC
        g, devops-bot, role:admin

      policy.infra-main.csv: |
        p, role:Infra_Main_RBAC, applications, *, */*, allow
        p, role:Infra_Main_RBAC, clusters, get, *, allow
        p, role:Infra_Main_RBAC, clusters, update, *, allow
        p, role:Infra_Main_RBAC, repositories, get, *, allow
        p, role:Infra_Main_RBAC, repositories, create, *, allow
        p, role:Infra_Main_RBAC, repositories, update, *, allow
        p, role:Infra_Main_RBAC, repositories, delete, *, allow
        p, role:Infra_Main_RBAC, projects, *, *, allow
        p, role:Infra_Main_RBAC, applicationsets, *, */*, allow
        p, role:Infra_Main_RBAC, certificates, get, *, allow
        p, role:Infra_Main_RBAC, accounts, get, *, allow
        p, role:Infra_Main_RBAC, logs, get, *, allow
        p, role:Infra_Main_RBAC, exec, create, *, allow

      ########################################################################################
      # ArgoCD - Platform Teams - RBAC Management
      ########################################################################################
      # ********-1f06-4c32-b70c-77a23866fbf9 : AzureGroup insp-platform-aqa
      # c740b6eb-aef8-405b-bb95-c7873190d329 : AzureGroup insp-platform-arb
      policy.platform-roles.csv: |
        g, "********-1f06-4c32-b70c-77a23866fbf9", role:Platform_Main_RBAC
        g, "c740b6eb-aef8-405b-bb95-c7873190d329", role:Platform_Main_RBAC

      policy.platform-main.csv: |
        p, role:Platform_Main_RBAC, clusters, get, *, allow
        p, role:Platform_Main_RBAC, projects, get, *, allow
        p, role:Platform_Main_RBAC, applications, get, */*, allow
        p, role:Platform_Main_RBAC, applications, sync, */*, allow
        p, role:Platform_Main_RBAC, logs, get, */*, allow


      ########################################################################################
      # ArgoCD - Security - RBAC Management
      ########################################################################################
      # fdc10e73-82ef-4003-b8af-2b36f7ce7e6c : AzureGroup insp-security
      policy.security-roles.csv: |
        g, "fdc10e73-82ef-4003-b8af-2b36f7ce7e6c", role:Security_Main_RBAC

      policy.security-main.csv: |
        p, role:Security_Main_RBAC, clusters, get, *, allow
        p, role:Security_Main_RBAC, projects, get, *, allow
        p, role:Security_Main_RBAC, applications, get, */*, allow
        p, role:Security_Main_RBAC, applications, sync, */*, allow
        p, role:Security_Main_RBAC, repositories, get, *, allow
        p, role:Security_Main_RBAC, logs, get, */*, allow
        p, role:Security_Main_RBAC, applicationsets, get, */*, allow
        p, role:Security_Main_RBAC, certificates, get, *, allow
        p, role:Security_Main_RBAC, accounts, get, *, allow
        p, role:Security_Main_RBAC, exec, create, sec-*/*, allow


      ########################################################################################
      # ArgoCD - CSS Product - RBAC Management
      ########################################################################################
      # 8b9c7b7b-bf29-47a6-8f38-219baa79db17 : AzureGroup insp-css-maple
      # 242fc72a-a7e8-4cf7-9f3b-394778ae4e2a : AzureGroup insp-css-core
      # 8df61d7e-9ef0-43d2-9a8c-b1fe7314d4b6 : AzureGroup insp-css-maple-coordinations
      # b170cb9c-63e4-4683-9d5e-77847dac2bac : AzureGroup insp-css-core-coordinations
      policy.css-roles.csv: |
        g, "8b9c7b7b-bf29-47a6-8f38-219baa79db17", role:CSS_Main_RBAC
        g, "242fc72a-a7e8-4cf7-9f3b-394778ae4e2a", role:CSS_Main_RBAC
        g, "8df61d7e-9ef0-43d2-9a8c-b1fe7314d4b6", role:CSS_Coordination_RBAC
        g, "b170cb9c-63e4-4683-9d5e-77847dac2bac", role:CSS_Coordination_RBAC

      policy.css-main.csv: |
        p, role:CSS_Main_RBAC, clusters, get, *, allow
        p, role:CSS_Main_RBAC, projects, get, css-*, allow
        p, role:CSS_Main_RBAC, applications, get, css-*/*, allow
        p, role:CSS_Main_RBAC, applications, sync, css-*/*, allow
        p, role:CSS_Main_RBAC, logs, get, css-*/*, allow
        p, role:CSS_Main_RBAC, exec, create, css-*/*, allow
        p, role:CSS_Main_RBAC, projects, get, core-*, allow
        p, role:CSS_Main_RBAC, applications, get, core-*/*, allow
        p, role:CSS_Main_RBAC, applications, sync, core-*/*, allow
        p, role:CSS_Main_RBAC, logs, get, core-*/*, allow
        p, role:CSS_Main_RBAC, exec, create, core-*/*, allow

      policy.css-coordination.csv: |
        p, role:CSS_Coordination_RBAC, clusters, get, *, allow
        p, role:CSS_Coordination_RBAC, projects, get, css-*, allow
        p, role:CSS_Coordination_RBAC, applications, get, css-*/*, allow
        p, role:CSS_Coordination_RBAC, applications, sync, css-*/*, allow
        p, role:CSS_Coordination_RBAC, logs, get, css-*/*, allow
        p, role:CSS_Coordination_RBAC, exec, create, css-*/*, allow
        p, role:CSS_Coordination_RBAC, projects, get, core-*, allow
        p, role:CSS_Coordination_RBAC, applications, get, core-*/*, allow
        p, role:CSS_Coordination_RBAC, applications, sync, core-*/*, allow
        p, role:CSS_Coordination_RBAC, logs, get, core-*/*, allow
        p, role:CSS_Coordination_RBAC, exec, create, core-*/*, allow


      ########################################################################################
      # ArgoCD - PM Product - RBAC Management
      ########################################################################################
      # 3020f1d8-fa2e-4677-b666-89595d6e22cc : AzureGroup insp-pm-marines
      # d972525b-c999-464d-84cf-1db6e8c4c45c : AzureGroup insp-pm-marines-coordinations
      policy.pm-roles.csv: |
        g, "3020f1d8-fa2e-4677-b666-89595d6e22cc", role:PM_Main_RBAC
        g, "d972525b-c999-464d-84cf-1db6e8c4c45c", role:PM_Coordination_RBAC

      policy.pm-main.csv: |
        p, role:PM_Main_RBAC, clusters, get, *, allow
        p, role:PM_Main_RBAC, projects, get, pm-*, allow
        p, role:PM_Main_RBAC, logs, get, pm-*/*, allow
        p, role:PM_Main_RBAC, exec, create, pm-*/*, allow
        p, role:PM_Main_RBAC, applications, get, pm-*/*, allow
        p, role:PM_Main_RBAC, applications, sync, pm-*/*, allow
        p, role:PM_Main_RBAC, applications, action/apps/Deployment/restart, pm-pre/*, allow
        p, role:PM_Main_RBAC, applications, action/apps/Deployment/restart, pm-stg/*, allow

      policy.pm-coordination.csv: |
        p, role:PM_Coordination_RBAC, clusters, get, *, allow
        p, role:PM_Coordination_RBAC, projects, get, pm-*, allow
        p, role:PM_Coordination_RBAC, logs, get, pm-*/*, allow
        p, role:PM_Coordination_RBAC, exec, create, pm-*/*, allow
        p, role:PM_Coordination_RBAC, applications, get, pm-*/*, allow
        p, role:PM_Coordination_RBAC, applications, sync, pm-*/*, allow
        p, role:PM_Coordination_RBAC, applications, action/apps/Deployment/restart, pm-pre/*, allow
        p, role:PM_Coordination_RBAC, applications, action/apps/Deployment/restart, pm-stg/*, allow

      ########################################################################################
      # ArgoCD - DnA (Data and Analytic) Product - RBAC Management
      ########################################################################################
      # d140b204-fa00-454a-aa80-f57d60437a07 : AzureGroup insp-dna-pepper
      # ff534345-36e8-4b67-8751-f2b4bf1c762f : AzureGroup insp-dna-ginger
      # c6e04cdd-7244-4d93-acf4-b3bd4b6cd012 : AzureGroup insp-data-science
      # a16e137e-5967-4dae-80ca-5cf0d8c2b662 : AzureGroup insp-dna-pepper-coordinations
      # e38a90d4-266e-412d-a09b-3634fc4976f0 : AzureGroup insp-dna-ginger-coordinations
      policy.dna-roles.csv: |
        g, "d140b204-fa00-454a-aa80-f57d60437a07", role:DNA_Main_RBAC
        g, "ff534345-36e8-4b67-8751-f2b4bf1c762f", role:DNA_Main_RBAC
        g, "c6e04cdd-7244-4d93-acf4-b3bd4b6cd012", role:DNA_Main_RBAC
        g, "a16e137e-5967-4dae-80ca-5cf0d8c2b662", role:DNA_Coordination_RBAC
        g, "e38a90d4-266e-412d-a09b-3634fc4976f0", role:DNA_Coordination_RBAC

      policy.dna-main.csv: |
        p, role:DNA_Main_RBAC, clusters, get, *, allow
        p, role:DNA_Main_RBAC, projects, get, dna-*, allow
        p, role:DNA_Main_RBAC, applications, get, dna-*/*, allow
        p, role:DNA_Main_RBAC, applications, sync, dna-*/*, allow
        p, role:DNA_Main_RBAC, logs, get, dna-*/*, allow
        p, role:DNA_Main_RBAC, exec, create, dna-*/*, allow
        p, role:DNA_Main_RBAC, applications, action/apps/Deployment/restart, dna-pre/*, allow
        p, role:DNA_Main_RBAC, applications, action/apps/Deployment/restart, dna-stg/*, allow

      policy.dna-coordination.csv: |
        p, role:DNA_Coordination_RBAC, clusters, get, *, allow
        p, role:DNA_Coordination_RBAC, projects, get, dna-*, allow
        p, role:DNA_Coordination_RBAC, applications, get, dna-*/*, allow
        p, role:DNA_Coordination_RBAC, applications, sync, dna-*/*, allow
        p, role:DNA_Coordination_RBAC, logs, get, dna-*/*, allow
        p, role:DNA_Coordination_RBAC, applications, action/apps/Deployment/restart, dna-pre/*, allow
        p, role:DNA_Coordination_RBAC, applications, action/apps/Deployment/restart, dna-stg/*, allow

      ########################################################################################
      # ArgoCD - QRM Product - RBAC Management
      ########################################################################################
      # 7ffb69bd-9a43-41ec-91d4-582e7c79ebe3 : AzureGroup insp-qrm-larvas
      # 6d8198b6-a16b-4959-b4c5-754fc70c063c : AzureGroup insp-qrm-oranges
      # 62d23502-ebd5-4230-b67d-4a5de1fad903 : AzureGroup insp-qrm-olympus
      # 81f22626-2512-4da3-963e-8af65744bd98 : AzureGroup insp-qrm-larvas-coordinations
      # 83fc9d0d-a6d3-40dc-99b6-0d95a201fab3 : AzureGroup insp-qrm-oranges-coordinations
      policy.qrm-roles.csv: |
        g, "7ffb69bd-9a43-41ec-91d4-582e7c79ebe3", role:QRM_Main_RBAC
        g, "6d8198b6-a16b-4959-b4c5-754fc70c063c", role:QRM_Main_RBAC
        g, "62d23502-ebd5-4230-b67d-4a5de1fad903", role:QRM_Main_RBAC
        g, "81f22626-2512-4da3-963e-8af65744bd98", role:QRM_Coordination_RBAC
        g, "83fc9d0d-a6d3-40dc-99b6-0d95a201fab3", role:QRM_Coordination_RBAC

      policy.qrm-main.csv: |
        p, role:QRM_Main_RBAC, clusters, get, *, allow
        p, role:QRM_Main_RBAC, projects, get, qrm-*, allow
        p, role:QRM_Main_RBAC, applications, get, qrm-*/*, allow
        p, role:QRM_Main_RBAC, applications, sync, qrm-*/*, allow
        p, role:QRM_Main_RBAC, logs, get, qrm-*/*, allow
        p, role:QRM_Main_RBAC, exec, create, qrm-*/*, allow
        p, role:QRM_Main_RBAC, applications, action/apps/Deployment/restart, qrm-pre/*, allow
        p, role:QRM_Main_RBAC, applications, action/apps/Deployment/restart, qrm-stg/*, allow

      policy.qrm-coordination.csv: |
        p, role:QRM_Coordination_RBAC, clusters, get, *, allow
        p, role:QRM_Coordination_RBAC, projects, get, qrm-*, allow
        p, role:QRM_Coordination_RBAC, applications, get, qrm-*/*, allow
        p, role:QRM_Coordination_RBAC, applications, sync, qrm-*/*, allow
        p, role:QRM_Coordination_RBAC, logs, get, qrm-*/*, allow
        p, role:QRM_Coordination_RBAC, exec, create, qrm-*/*, allow
        p, role:QRM_Coordination_RBAC, applications, action/apps/Deployment/restart, qrm-pre/*, allow
        p, role:QRM_Coordination_RBAC, applications, action/apps/Deployment/restart, qrm-stg/*, allow

      ########################################################################################
      # ArgoCD - RSC Product - RBAC Management
      ########################################################################################
      # 6357e3bb-271a-4412-935e-8d1bfae6fd3d : AzureGroup insp-rsc-durians
      # ef1f78d6-a9db-4ce2-be9b-044bff77b6fa : AzureGroup insp-rsc-kiwis
      # 1c39d607-047b-42ed-a64a-d307eba5f206 : AzureGroup insp-rsc-kiwis-coordinations
      # a1a103e7-124e-40b1-994c-b42ec0ef89a4 : AzureGroup insp-rsc-durians-coordinations
      policy.rsc-roles.csv: |
        g, "6357e3bb-271a-4412-935e-8d1bfae6fd3d", role:RSC_Main_RBAC
        g, "ef1f78d6-a9db-4ce2-be9b-044bff77b6fa", role:RSC_Main_RBAC
        g, "6487818d-1b7e-4eb0-8aee-b6723a410840", role:RSC_Main_RBAC
        g, "1c39d607-047b-42ed-a64a-d307eba5f206", role:RSC_Coordination_RBAC
        g, "a1a103e7-124e-40b1-994c-b42ec0ef89a4", role:RSC_Coordination_RBAC

      policy.rsc-main.csv: |
        p, role:RSC_Main_RBAC, clusters, get, *, allow
        p, role:RSC_Main_RBAC, projects, get, rsc-*, allow
        p, role:RSC_Main_RBAC, applications, get, rsc-*/*, allow
        p, role:RSC_Main_RBAC, applications, sync, rsc-*/*, allow
        p, role:RSC_Main_RBAC, logs, get, rsc-*/*, allow
        p, role:RSC_Main_RBAC, exec, create, rsc-*/*, allow
        p, role:RSC_Main_RBAC, applications, action/batch/CronJob/create-job, rsc-prd/*, allow
        p, role:RSC_Main_RBAC, applications, action/apps/Deployment/restart, rsc-pre/*, allow
        p, role:RSC_Main_RBAC, applications, action/*/*, rsc-pre/*, allow
        p, role:RSC_Main_RBAC, applications, action/apps/Deployment/restart, rsc-stg/*, allow
        p, role:RSC_Main_RBAC, applications, action/*/*, rsc-stg/*, allow

      policy.rsc-coordination.csv: |
        p, role:RSC_Coordination_RBAC, clusters, get, *, allow
        p, role:RSC_Coordination_RBAC, projects, get, rsc-*, allow
        p, role:RSC_Coordination_RBAC, applications, get, rsc-*/*, allow
        p, role:RSC_Coordination_RBAC, applications, sync, rsc-*/*, allow
        p, role:RSC_Coordination_RBAC, logs, get, rsc-*/*, allow
        p, role:RSC_Coordination_RBAC, exec, create, rsc-*/*, allow
        p, role:RSC_Coordination_RBAC, applications, action/apps/Deployment/restart, rsc-pre/*, allow
        p, role:RSC_Coordination_RBAC, applications, action/apps/Deployment/restart, rsc-stg/*, allow

argocd-genapp:
  serviceAccount:
    create: true
    name: argocd-genapp
    enableWorkloadIdentity: true

  externalSecretStore:
    gke-stg-cluster-connection:
      template:
        engineVersion: v2
        data:
          name: "stg"
          server: "{{ .server }}" # update this one in secret when have new ip of gke cluster
          config: "{{ .config }}"
          shard: "0"
        metadata:
          labels:
            argocd.argoproj.io/secret-type: cluster
            app.inspectorio.com/maintainer: sre
            app.inspectorio.com/owner: sre
            app.inspectorio.com/product: infra
            app.inspectorio.com/service: argocd
            upgrade: v1
      source:
        dataFrom:
          - extract:
              key: argocd-gke-stg-cluster-connection-config
              conversionStrategy: Default
              decodingStrategy: None

    gke-pre-cluster-connection:
      template:
        engineVersion: v2
        data:
          name: "pre"
          server: "{{ .server }}" # update this one in secret when have new ip of gke cluster
          config: "{{ .config }}"
          shard: "1"
        metadata:
          labels:
            argocd.argoproj.io/secret-type: cluster
            app.inspectorio.com/maintainer: sre
            app.inspectorio.com/owner: sre
            app.inspectorio.com/product: infra
            app.inspectorio.com/service: argocd
            upgrade: v1
      source:
        dataFrom:
          - extract:
              key: argocd-gke-pre-cluster-connection-config
              conversionStrategy: Default
              decodingStrategy: None

    gke-prd-cluster-connection:
      template:
        engineVersion: v2
        data:
          name: "prd"
          server: "{{ .server }}" # update this one in secret when have new ip of gke cluster
          config: "{{ .config }}"
          shard: "2"
        metadata:
          labels:
            argocd.argoproj.io/secret-type: cluster
            app.inspectorio.com/maintainer: sre
            app.inspectorio.com/owner: sre
            app.inspectorio.com/product: infra
            app.inspectorio.com/service: argocd
      source:
        dataFrom:
          - extract:
              key: argocd-gke-prd-cluster-connection-config
              conversionStrategy: Default
              decodingStrategy: None

    gke-ant-cluster-connection:
      template:
        engineVersion: v2
        data:
          name: "ant"
          server: "{{ .server }}" # update this one in secret when have new ip of gke cluster
          config: "{{ .config }}"
          shard: "3"
        metadata:
          labels:
            argocd.argoproj.io/secret-type: cluster
            app.inspectorio.com/maintainer: sre
            app.inspectorio.com/owner: sre
            app.inspectorio.com/product: infra
            app.inspectorio.com/service: argocd
      source:
        dataFrom:
          - extract:
              key: argocd-gke-ant-cluster-connection-config
              conversionStrategy: Default
              decodingStrategy: None

    # gke-prd2-cluster-connection: # definition for dr cluster
    #   refreshInterval: "60s" # only auto interval refresh for prd2 disaster recovery
    #   template:
    #     engineVersion: v2
    #     data:
    #       name: "prd2"
    #       server: "{{ .server }}" # update this one in secret when have new ip of gke cluster
    #       config: "{{ .config }}"
    #       shard: "4"
    #     metadata:
    #       labels:
    #         argocd.argoproj.io/secret-type: cluster
    #         app.inspectorio.com/maintainer: sre
    #         app.inspectorio.com/owner: sre
    #         app.inspectorio.com/product: infra
    #         app.inspectorio.com/service: argocd
    #   source:
    #     dataFrom:
    #       - extract:
    #           key: argocd-gke-prd2-cluster-connection-config
    #           conversionStrategy: Default
    #           decodingStrategy: None

    private-repo-gitops-cd:
      template:
        engineVersion: v2
        data:
          url: "**************************:devops/gitops-cd.git"
          insecure: "true"
          name: "gitops-cd"
          type: "git"
          sshPrivateKey: "{{ .ssh | b64dec }}"
        metadata:
          labels:
            argocd.argoproj.io/secret-type: repository
            app.inspectorio.com/maintainer: sre
            app.inspectorio.com/owner: sre
            app.inspectorio.com/product: infra
            app.inspectorio.com/service: argocd
      source:
        data:
          - secretKey: ssh
            remoteRef:
              key: argocd-gitlab-ssh-key

    argocd-gitlab-token: # used for generator pull request - preview environment
      template:
        engineVersion: v2
        data:
          token: "{{ .token }}"
        metadata:
          labels:
            app.inspectorio.com/maintainer: sre
            app.inspectorio.com/owner: sre
            app.inspectorio.com/product: infra
            app.inspectorio.com/service: argocd
      source:
        data:
          - secretKey: token
            remoteRef:
              key: argocd-gitlab-token

    argocd-azure-app-oidc: # used for login sso
      template:
        engineVersion: v2
        data:
          "oidc.azuread.clientSecret": "{{ .secret }}"
        metadata:
          labels:
            app.inspectorio.com/maintainer: sre
            app.inspectorio.com/owner: sre
            app.inspectorio.com/product: infra
            app.inspectorio.com/service: argocd
            app.kubernetes.io/part-of: argocd
      source:
        data:
          - secretKey: secret
            remoteRef:
              key: argocd-azure-app-oidc

    argocd-notifications-secret:
      template:
        engineVersion: v2
        data:
          "slack-token": "{{ .secret }}"
        metadata:
          labels:
            app.inspectorio.com/maintainer: sre
            app.inspectorio.com/owner: sre
            app.inspectorio.com/product: infra
            app.inspectorio.com/service: argocd
            app.kubernetes.io/part-of: argocd
            app.kubernetes.io/component: notifications-controller
            app.kubernetes.io/name: argocd-notifications-controller
      source:
        data:
          - secretKey: secret
            remoteRef:
              key: argocd-slack-token

  istio:
    ingress:
      default:
        enabled: True
        gateways:
          - common
        hosts:
          - argocd.gcp-ant.inspectorio.com
        customHttpRoutes:
          pre:
          - match:
              - headers:
                  traffic-class:
                    regex: ".*(inspectorio|cluster).*"
            route:
              - destination:
                  host: argocd-server.argocd.svc.cluster.local
                  port:
                    number: 80
            retries:
              attempts: 5
              perTryTimeout: 60s
              retryOn: 'gateway-error,connect-failure,refused-stream,reset,5xx'
        http:
          - directResponse:
              status: 403
              body:
                string: "You need to use authorized network (VPN) to access this service. Please contact administrator !"
