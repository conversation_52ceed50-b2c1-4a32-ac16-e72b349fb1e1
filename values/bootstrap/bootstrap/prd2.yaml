namespaceArgocd: argocd
cluster: ant #cluster to deploy argo crd's
targetCluster: prd2 # cluster to deploy environments
envState: prd #environment name on a targetCluster
gitlabUrl: https://gitlab.inspectorio.com
repoUrl: **************************:devops/gitops-cd.git
monoRepoUrl: &monorepo https://gitlab.inspectorio.com/saas/monorepo.git
featureDomainSuffux: .gcp-pre.inspectorio.com
featureDomainSecretName: gcp-pre-inspectorio-com
clusterBootstrapValues: ../../../../../envs/prd2/values.yaml
productOrgValues:
  - "../../../../../envs/org/css.yaml"
  - "../../../../../envs/org/dna.yaml"
  - "../../../../../envs/org/infra.yaml"
  - "../../../../../envs/org/platform.yaml"
  - "../../../../../envs/org/pm.yaml"
  - "../../../../../envs/org/qrm.yaml"
  - "../../../../../envs/org/rsc.yaml"
gitlabTokenSecretname: #argocd-gitlab-token
defaultDeliveryOption: manual

syncPolicy:
  syncOptions:
    - Validate=true
    - CreateNamespace=true
    - PrunePropagationPolicy=orphan
    - PruneLast=true
    - ServerSideApply=true
  retry:
    limit: 5
    backoff:
      duration: 5s
      factor: 2
      maxDuration: 3m

previewSyncPolicy:
  automated: # automated sync by default retries failed attempts 5 times with following delays between attempts ( 5s, 10s, 20s, 40s, 80s ); retry controlled using `retry` field.
    prune: true # Specifies if resources should be pruned during auto-syncing ( false by default ).
    selfHeal: true # Specifies if partial app sync should be executed when resources are changed only in target Kubernetes cluster and no git change detected ( false by default ).
    allowEmpty: true # Allows deleting all application resources during automatic syncing ( false by default ).
  syncOptions: # Sync options which modifies sync behavior
    - Validate=true # disables resource validation (equivalent to 'kubectl apply --validate=false') ( true by default ).
    - CreateNamespace=false # Namespace Auto-Creation ensures that namespace specified as the application destination exists in the destination cluster.
    - PrunePropagationPolicy=foreground # Supported policies are background, foreground and orphan.
    - PruneLast=true # Allow the ability for resource pruning to happen as a final, implicit wave of a sync operation
    - ServerSideApply=true
    - ApplyOutOfSyncOnly=true
  retry:
    limit: 5 # number of failed sync attempt retries; unlimited number of attempts if less than 0
    backoff:
      duration: 5s # the amount to back off. Default unit is seconds, but could also be a duration (e.g. "2m", "1h")
      factor: 2 # a factor to multiply the base duration after each failed retry
      maxDuration: 15m # the maximum amount of time allowed for the backoff strategy

release_channels:
  stable: manual

projects:
  prd2:
    apps:
      bootstrap:
        meta: true
        syncWave: 1
        overwriteNamespace: argocd
        targetClusterOverride: ant
        envState: prd2
      baseline:
        syncWave: 1
        envState: prd2
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
        overwriteNamespace: default

  gitlab:
    apps:
      gitlab-runner:
        syncWave: 1
        overwriteNamespace: gitlab-runner

  integration:
    apps:
      sftpgo:
        syncWave: 2
        overwriteNamespace: sftpgo

  ds:
    additional_namespaces:
      - kafka
    apps:
      analytics-superset:
        syncWave: 10
        syncPolicy:
          automated:
            prune: true
            selfHeal: true
            #allowEmpty: false
          syncOptions:
            - Validate=true
            - CreateNamespace=false
            - PrunePropagationPolicy=foreground
            - PruneLast=true
            - ServerSideApply=false
        release_channels:
          stable: on_success
        overwriteNamespace: superset
      kafka-connect-cdc:
        syncWave: 10
        overwriteNamespace: kafka-connect-cdc
      clickhouse-ds:
        syncWave: 10
        overwriteNamespace: clickhouse-ds
      product-risk-be:
        syncWave: 51
        overwriteNamespace: product-risk
        release_channels:
          stable: on_success
      pub-capa:
        syncWave: 53
        overwriteNamespace: pub-capa
        release_channels:
          stable: on_success
      defect-recommend-be:
        syncWave: 10
        overwriteNamespace: defect-recommend-be
        release_channels:
          stable: on_success
      qa-platform:
        syncWave: 50
        overwriteNamespace: qa-platform
      factory-risk-be:
        syncWave: 51
        overwriteNamespace: factory-risk-be
        release_channels:
          stable: on_success
      airflow:
        syncWave: 50
        overwriteNamespace: airflow
      dedupe-api:
        syncWave: 50
        overwriteNamespace: dedupe-api
      timing-formula:
        syncWave: 50
        overwriteNamespace: timing-formula
      analytic3:
        syncWave: 20
        overwriteNamespace: analytic3
      document-validator:
        syncWave: 50
        overwriteNamespace: document-validator
        release_channels:
          stable: on_success

  devops:
    notification:
      enabled: false
    additional_namespaces:
      - kube-system
      - istio-system
      - kafka
      - product-risk
      - default
      - remote-config
      - kafka-connect-cdc
      - mobileresponder
      - passport
      - car
      - defect-recommend-be
      - tracking
      - sight-be
      - qa-platform
      - zendesk
      - clickhouse-ds
      - dm
    apps:
      istio:
        syncWave: 1
        envState: prd2
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
        overwriteNamespace: istio-system
        disableClusterBootstrapValues: true
        ignoreDifferencesAdditional:
          - group: apps
            kind: Deployment
            jqPathExpressions:
              - .spec.template.spec.containers[].env[].valueFrom.resourceFieldRef.divisor
      certificates:
        syncWave: 1
        overwriteNamespace: cert-manager
      pomerium:
        syncWave: 2
        envState: prd2
        overwriteNamespace: pomerium
      monitoring:
        syncWave: 10
        overwriteNamespace: monitoring
      kafka-operator:
        syncWave: 3
        overwriteNamespace: kafka-operator
      kafka:
        syncWave: 10
        overwriteNamespace: kafka
      patch-operator:
        syncWave: 4
        overwriteNamespace: patch-operator
      alerting:
        syncWave: 15
      pgbouncer:
        syncWave: 5
        overwriteNamespace: pgbouncer
      eck-operator:
        syncWave: 3
        overwriteNamespace: eck-operator
      clickhouse-operator:
        syncWave: 12
        overwriteNamespace: clickhouse-operator
        ignoreDifferencesAdditional:
          - group: "apiextensions.k8s.io"
            kind: CustomResourceDefinition
            jqPathExpressions:
              - .spec.versions[].additionalPrinterColumns[].priority
          - group: apps
            kind: Deployment
            jqPathExpressions:
              - .spec.template.spec.containers[].env[].valueFrom.resourceFieldRef.divisor
      eck-sight:
        syncWave: 14
        overwriteNamespace: eck-sight
      eck-integration:
        syncWave: 14
        overwriteNamespace: eck-integration
      eck-infra:
        syncWave: 9
        overwriteNamespace: eck-infra
      datadog:
        syncWave: 6
        overwriteNamespace: datadog
      external-secrets:
        syncWave: 1
        overwriteNamespace: external-secrets
      kong:
        syncWave: 1
        overwriteNamespace: kong
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
      kong-gateway:
        syncWave: 1
        envState: prd2
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
        overwriteNamespace: kong
        ignoreDifferencesAdditional:
          - group: ""
            kind: Service
            namespace: kong
            jqPathExpressions:
              - .spec.ports[].nodePort
          - kind: Secret
            name: kong-gateway-ingress-api-validation-webhook-keypair
            namespace: kong
            jsonPointers:
              - /data
          - kind: Secret
            name: kong-gateway-ingress-api-validation-webhook-ca-keypair
            namespace: kong
            jsonPointers:
              - /data
      kafka-ui:
        syncWave: 7
        envState: prd2
        overwriteNamespace: kafka-ui
      redis-ui:
        syncWave: 8
        overwriteNamespace: redis-ui
      victoria-metrics:
        syncWave: 10
        overwriteNamespace: victoria-metrics
        ignoreDifferencesAdditional:
          - group: "apps"
            kind: StatefulSet
            jqPathExpressions:
              - .spec.volumeClaimTemplates[].apiVersion
              - .spec.volumeClaimTemplates[].kind
      fluentd:
        syncWave: 10
        overwriteNamespace: fluentd
      external-service-proxy:
        syncWave: 10
        overwriteNamespace: external-service-proxy
      gcs-files-upload:
        syncWave: 10
        overwriteNamespace: gcs-files-upload
      imageproxy:
        syncWave: 11
        overwriteNamespace: imageproxy
        release_channels:
          stable: on_success
      memcached-admin:
        syncWave: 11
        overwriteNamespace: memcached-admin
      gcp-notify:
        syncWave: 12
        release_channels:
          stable: on_success
      keda:
        syncWave: 13
        overwriteNamespace: keda
        ignoreDifferencesAdditional:
          # https://github.com/kedacore/keda/issues/4732
          - group: apiregistration.k8s.io
            kind: APIService
            name: v1beta1.external.metrics.k8s.io
            jsonPointers:
              - /spec/insecureSkipTLSVerify
      devops-exporter:
        syncWave: 12
      kubecost:
        syncWave: 13
        overwriteNamespace: kubecost

  core:
    apps:
      hermes-be:
        syncWave: 31
        overwriteNamespace: hermes
        release_channels:
          stable: on_success
      portal:
        syncWave: 24
        overwriteNamespace: portal
        release_channels:
          stable: on_success
      kong-auth-router:
        syncWave: 32
        overwriteNamespace: default
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
      hermes-fe:
        syncWave: 33
        overwriteNamespace: hermes
        release_channels:
          stable: on_success
      passport-be:
        syncWave: 34
        overwriteNamespace: passport
        release_channels:
          stable: on_success
      bouncers:
        syncWave: 35
        overwriteNamespace: bouncers
        release_channels:
          stable: on_success
      fms:
        syncWave: 36
        overwriteNamespace: fms
        release_channels:
          stable: on_success
        customize:
          helm:
            parameters:
              - name: fms.externalSecretStore.app.source.data[0].remoteRef.key
                value: fms-sms-secret-key
              - name: fms.externalSecretStore.app.source.data[0].secretKey
                value: SECRET_KEY
              - name: fms.externalSecretStore.app.source.data[1].remoteRef.property
                value: uri
              - name: fms.externalSecretStore.app.source.data[1].remoteRef.key
                value: mongodb-atlas-fms-prd2
              - name: fms.externalSecretStore.app.source.data[1].secretKey
                value: MONGODB_HOST
              - name: fms.externalSecretStore.app.source.data[2].remoteRef.key
                value: fms-sentry-default
              - name: fms.externalSecretStore.app.source.data[2].secretKey
                value: SENTRY_DSN
              - name: fms.externalSecretStore.app.source.data[3].remoteRef.key
                value: fms-google-cloud-sa-credentials
              - name: fms.externalSecretStore.app.source.data[3].secretKey
                value: GOOGLE_SA_CREDENTIALS
      permission-dashboard:
        syncWave: 37
        overwriteNamespace: permission-dashboard
        release_channels:
          stable: on_success
      sms:
        syncWave: 38
        overwriteNamespace: sms
        release_channels:
          stable: on_success
      dev-portal:
        syncWave: 39
        overwriteNamespace: dev-portal
        release_channels:
          stable: on_success
      chatbot-be:
        syncWave: 40
        overwriteNamespace: chatbot-be
        release_channels:
          stable: on_success

  # New structure product naming parts: pm, rsc, qrm, css, dna
  css:
    apps:
      org-resolver:
        syncWave: 30
        overwriteNamespace: org-resolver
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      subscription-fe:
        syncWave: 30
        overwriteNamespace: subscription
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      subscription-be:
        syncWave: 30
        overwriteNamespace: subscription
        migrationNamingEnable: true
        release_channels:
          stable: on_success

  pm:
    additional_namespaces:
      - kafka
    apps:
      tracking:
        syncWave: 90
        overwriteNamespace: tracking
        migrationNamingEnable: true
        release_channels:
          stable: on_success

  rsc:
    additional_namespaces:
      - kafka
    apps:
      userssync:
        syncWave: 34
        overwriteNamespace: userssync
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      translation:
        syncWave: 35
        overwriteNamespace: translation
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      rs-backend:
        syncWave: 36
        overwriteNamespace: rs
        migrationNamingEnable: true
      rise-integration-api:
        syncWave: 37
        overwriteNamespace: rs
        migrationNamingEnable: true
      rs-frontend:
        syncWave: 38
        overwriteNamespace: rs
        migrationNamingEnable: true
      collabora-online:
        syncWave: 2
        migrationNamingEnable: true
        overwriteNamespace: dm
      dm-backend:
        syncWave: 20
        migrationNamingEnable: true
        overwriteNamespace: dm
        ignoreDifferencesAdditional:
          - group: "redis.redis.opstreelabs.in"
            kind: RedisReplication
            jqPathExpressions:
              - .spec.storage.volumeClaimTemplate.metadata.name
        release_channels:
          stable: on_success
      dm-frontend:
        syncWave: 11
        migrationNamingEnable: true
        overwriteNamespace: dm
        release_channels:
          stable: on_success
      dm-metrics:
        syncWave: 20
        migrationNamingEnable: true
        overwriteNamespace: dm
        release_channels:
          stable: on_success
      thirdparty-reports:
        syncWave: 30
        overwriteNamespace: thirdparty-reports
        migrationNamingEnable: true
        release_channels:
          stable: on_success

  qrm:
    additional_namespaces:
      - kafka
    apps:
      emlauncher-be:
        syncWave: 45
        overwriteNamespace: emlauncher
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      emlauncher-fe:
        syncWave: 45
        overwriteNamespace: emlauncher
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      master-data:
        syncWave: 41
        overwriteNamespace: master-data
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      mobileresponder:
        syncWave: 44
        overwriteNamespace: mobileresponder
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      remote-config:
        syncWave: 43
        overwriteNamespace: remote-config
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      zendesk-sso:
        syncWave: 45
        overwriteNamespace: zendesk
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      car:
        syncWave: 49
        overwriteNamespace: car
        migrationNamingEnable: true
        release_channels:
          stable: on_success
        customize:
          helm:
            parameters:
              - name: car.externalSecretStore.car.source.data[0].remoteRef.key
                value: mongodb-atlas-car-prd2
              - name: car.externalSecretStore.car.source.data[0].secretKey
                value: MONGODB_URI
              - name: car.externalSecretStore.car.source.data[0].remoteRef.property
                value: uri
              - name: car.externalSecretStore.car.source.data[1].remoteRef.key
                value: car-sms-jwt-secret-key
              - name: car.externalSecretStore.car.source.data[1].secretKey
                value: SECRET_KEY
              - name: car.externalSecretStore.car.source.data[2].remoteRef.key
                value: car-sms-password
              - name: car.externalSecretStore.car.source.data[2].secretKey
                value: SMS_PASSWORD
              - name: car.externalSecretStore.car.source.data[3].remoteRef.key
                value: car-sentry-default
              - name: car.externalSecretStore.car.source.data[3].secretKey
                value: SENTRY_DSN
      notimanager:
        syncWave: 101
        overwriteNamespace: notimanager
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      integration-api:
        syncWave: 111
        overwriteNamespace: integration-api
        migrationNamingEnable: true
        release_channels:
          stable: on_success
        customize:
          helm:
            parameters:
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[0].remoteRef.key
                value: integration-api-sentry-default
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[0].secretKey
                value: SENTRY_DSN
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[1].remoteRef.key
                value: mongodb-atlas-integration-api-prd2
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[1].secretKey
                value: MONGODB_HOST
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[1].remoteRef.property
                value: uri
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[2].remoteRef.key
                value: passport-be-sms-jwt-secret-key
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[2].secretKey
                value: SECRET_KEY
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[3].remoteRef.key
                value: integration-api-sms-password
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[3].secretKey
                value: LOGIN_PASSWORD_SMS
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[4].remoteRef.key
                value: integration-api-aws-access-key-id
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[4].secretKey
                value: AWS_ACCESS_KEY_ID
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[5].remoteRef.key
                value: integration-api-aws-access-key-secret
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[5].secretKey
                value: AWS_SECRET_ACCESS_KEY
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[6].remoteRef.key
                value: integration-api-fms-api-key
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[6].secretKey
                value: FMS_API_KEY
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[7].remoteRef.key
                value: integration-api-slack-inditex-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[7].secretKey
                value: SLACK_INDITEX_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[8].remoteRef.key
                value: integration-api-slack-inditex-cd-meco-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[8].secretKey
                value: SLACK_INDITEX_CD_MECO_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[9].remoteRef.key
                value: integration-api-slack-inditex-cd-holanda-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[9].secretKey
                value: SLACK_INDITEX_CD_HOLANDA_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[10].remoteRef.key
                value: integration-api-slack-inditext-ladies-knitwear-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[10].secretKey
                value: SLACK_INDITEXT_LADIES_KNITWEAR_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[11].remoteRef.key
                value: integration-api-slack-inditex-cordigo-comprador-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[11].secretKey
                value: SLACK_INDITEX_CORDIGO_COMPRADOR_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[12].remoteRef.key
                value: integration-api-slack-inditex-cd-arteixo-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[12].secretKey
                value: SLACK_INDITEX_CD_ARTEIXO_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[13].remoteRef.key
                value: integration-api-slack-inditex-cd-cabanillas-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[13].secretKey
                value: SLACK_INDITEX_CD_CABANILLAS_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[14].remoteRef.key
                value: integration-api-slack-inditex-cd-leon-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[14].secretKey
                value: SLACK_INDITEX_CD_LEON_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[15].remoteRef.key
                value: integration-api-slack-inditex-global-caballero-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[15].secretKey
                value: SLACK_INDITEX_GLOBAL_CABALLERO_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[16].remoteRef.key
                value: integration-api-slack-inditex-circular-senora-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[16].secretKey
                value: SLACK_INDITEX_CIRCULAR_SENORA_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[17].remoteRef.key
                value: integration-api-slack-zara-nino-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[17].secretKey
                value: SLACK_ZARA_NINO_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[18].remoteRef.key
                value: integration-api-slack-zaragoza-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[18].secretKey
                value: SLACK_ZARAGOZA_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[19].remoteRef.key
                value: integration-api-slack-zara-home-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[19].secretKey
                value: SLACK_ZARA_HOME_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[20].remoteRef.key
                value: integration-api-slack-zara-trf-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[20].secretKey
                value: SLACK_ZARA_TRF_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[21].remoteRef.key
                value: integration-api-slack-brandart-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[21].secretKey
                value: SLACK_BRANDART_MONITORING_WEBHOOK
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[22].remoteRef.key
                value: integration-api-slack-stradivarius-cd-monitoring-webhook
              - name: integration-api.externalSecretStore.sight-integration-api.source.data[22].secretKey
                value: SLACK_STRADIVARIUS_CD_MONITORING_WEBHOOK
              
      report-html:
        syncWave: 45
        overwriteNamespace: report-html
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      sight-be:
        syncWave: 71
        overwriteNamespace: sight-be
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      frontend:
        syncWave: 92
        overwriteNamespace: frontend
        migrationNamingEnable: true
        release_channels:
          stable: on_success
      mobile-slackbot:
        syncWave: 47
        overwriteNamespace: mobile-slackbot
        migrationNamingEnable: true
        release_channels:
          stable: on_success
