namespaceArgocd: argocd
cluster: ant #cluster to deploy argo crd's
targetCluster: prd # cluster to deploy environments
envState: prd #environment name on a targetCluster
statePrefix: state/
gitlabUrl: https://gitlab.inspectorio.com
repoUrl: **************************:devops/gitops-cd.git
monoRepoUrl: &monorepo https://gitlab.inspectorio.com/saas/monorepo.git
featureDomainSuffux: .gcp-pre.inspectorio.com
featureDomainSecretName: gcp-pre-inspectorio-com
clusterBootstrapValues: "../../../../../envs/prd/values.yaml"
productOrgValues:
  - "../../../../../envs/org/css.yaml"
  - "../../../../../envs/org/dna.yaml"
  - "../../../../../envs/org/infra.yaml"
  - "../../../../../envs/org/platform.yaml"
  - "../../../../../envs/org/pm.yaml"
  - "../../../../../envs/org/qrm.yaml"
  - "../../../../../envs/org/rsc.yaml"
gitlabTokenSecretname: #argocd-gitlab-token
defaultDeliveryOption: manual

syncPolicy:
  automated: # automated sync by default retries failed attempts 5 times with following delays between attempts ( 5s, 10s, 20s, 40s, 80s ); retry controlled using `retry` field.
    prune: true # Specifies if resources should be pruned during auto-syncing ( false by default ).
    selfHeal: true # Specifies if partial app sync should be executed when resources are changed only in target Kubernetes cluster and no git change detected ( false by default ).
    #allowEmpty: false # Allows deleting all application resources during automatic syncing ( false by default ).
  syncOptions: # Sync options which modifies sync behavior
    - Validate=true # disables resource validation (equivalent to 'kubectl apply --validate=false') ( true by default ).
    - CreateNamespace=false # Namespace Auto-Creation ensures that namespace specified as the application destination exists in the destination cluster.
    - PrunePropagationPolicy=foreground # Supported policies are background, foreground and orphan.
    - PruneLast=true # Allow the ability for resource pruning to happen as a final, implicit wave of a sync operation
    - ServerSideApply=true
    - ApplyOutOfSyncOnly=true
  retry:
    limit: 5 # number of failed sync attempt retries; unlimited number of attempts if less than 0
    backoff:
      duration: 5s # the amount to back off. Default unit is seconds, but could also be a duration (e.g. "2m", "1h")
      factor: 2 # a factor to multiply the base duration after each failed retry
      maxDuration: 3m # the maximum amount of time allowed for the backoff strategy

previewSyncPolicy:
  automated: # automated sync by default retries failed attempts 5 times with following delays between attempts ( 5s, 10s, 20s, 40s, 80s ); retry controlled using `retry` field.
    prune: true # Specifies if resources should be pruned during auto-syncing ( false by default ).
    selfHeal: true # Specifies if partial app sync should be executed when resources are changed only in target Kubernetes cluster and no git change detected ( false by default ).
    allowEmpty: true # Allows deleting all application resources during automatic syncing ( false by default ).
  syncOptions: # Sync options which modifies sync behavior
    - Validate=true # disables resource validation (equivalent to 'kubectl apply --validate=false') ( true by default ).
    - CreateNamespace=false # Namespace Auto-Creation ensures that namespace specified as the application destination exists in the destination cluster.
    - PrunePropagationPolicy=foreground # Supported policies are background, foreground and orphan.
    - PruneLast=true # Allow the ability for resource pruning to happen as a final, implicit wave of a sync operation
    - ServerSideApply=true
    - ApplyOutOfSyncOnly=true
  retry:
    limit: 5 # number of failed sync attempt retries; unlimited number of attempts if less than 0
    backoff:
      duration: 5s # the amount to back off. Default unit is seconds, but could also be a duration (e.g. "2m", "1h")
      factor: 2 # a factor to multiply the base duration after each failed retry
      maxDuration: 15m # the maximum amount of time allowed for the backoff strategy

release_channels:
  stable: on_success

projects:
  gitlab:
    apps:
      gitlab-runner:
        syncWave: 1
        overwriteNamespace: gitlab-runner

  integration:
    apps:
      sftpgo:
        syncWave: 2
        overwriteNamespace: sftpgo

  prd:
    apps:
      bootstrap:
        meta: true
        syncWave: 1
        overwriteNamespace: argocd
        targetClusterOverride: ant
      baseline:
        syncWave: 1
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
        overwriteNamespace: default

  devops:
    notification:
      enabled: false
    additional_namespaces:
      - kube-system
      - istio-system
      - kafka
      - product-risk
      - default
      - remote-config
      - kafka-connect-cdc
      - mobileresponder
      - passport
      - car
      - defect-recommend-be
      - tracking
      - sight-be
      - qa-platform
      - zendesk
      - clickhouse-ds
      - dm
      - rs
    apps:
      istio:
        syncWave: 1
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
        overwriteNamespace: istio-system
        disableClusterBootstrapValues: true
        ignoreDifferencesAdditional:
          - group: apps
            kind: Deployment
            jqPathExpressions:
              - .spec.template.spec.containers[].env[].valueFrom.resourceFieldRef.divisor
      certificates:
        syncWave: 1
        overwriteNamespace: cert-manager
      pomerium:
        syncWave: 2
        overwriteNamespace: pomerium
      monitoring:
        syncWave: 10
        overwriteNamespace: monitoring
      kafka-operator:
        syncWave: 3
        overwriteNamespace: kafka-operator
      kafka:
        syncWave: 10
        overwriteNamespace: kafka
      kafka-schema-registry:
        syncWave: 11
        overwriteNamespace: kafka-schema-registry
      patch-operator:
        syncWave: 4
        overwriteNamespace: patch-operator
      alerting:
        syncWave: 15
      pgbouncer:
        syncWave: 5
        overwriteNamespace: pgbouncer
      eck-operator:
        syncWave: 3
        overwriteNamespace: eck-operator
      clickhouse-operator:
        syncWave: 12
        overwriteNamespace: clickhouse-operator
        ignoreDifferencesAdditional:
          - group: "apiextensions.k8s.io"
            kind: CustomResourceDefinition
            jqPathExpressions:
              - .spec.versions[].additionalPrinterColumns[].priority
          - group: apps
            kind: Deployment
            jqPathExpressions:
              - .spec.template.spec.containers[].env[].valueFrom.resourceFieldRef.divisor
      eck-sight:
        syncWave: 14
        overwriteNamespace: eck-sight
      eck-integration:
        syncWave: 14
        overwriteNamespace: eck-integration
      eck-infra:
        syncWave: 9
        overwriteNamespace: eck-infra
      eck-datasync:
        syncWave: 14
        overwriteNamespace: eck-datasync
      datadog:
        syncWave: 6
        overwriteNamespace: datadog
      datadog-gateway:
        syncWave: 6
        overwriteNamespace: datadog
      external-secrets:
        syncWave: 1
        overwriteNamespace: external-secrets
      kong:
        syncWave: 1
        overwriteNamespace: kong
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
      kong-gateway:
        syncWave: 1
        release_channels:
          stable: manual
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
        overwriteNamespace: kong
        ignoreDifferencesAdditional:
          - group: ""
            kind: Service
            namespace: kong
            jqPathExpressions:
              - .spec.ports[].nodePort
          - kind: Secret
            name: kong-gateway-ingress-api-validation-webhook-keypair
            namespace: kong
            jsonPointers:
              - /data
          - kind: Secret
            name: kong-gateway-ingress-api-validation-webhook-ca-keypair
            namespace: kong
            jsonPointers:
              - /data
      kafka-ui:
        syncWave: 7
        overwriteNamespace: kafka-ui
      redis-ui:
        syncWave: 8
        overwriteNamespace: redis-ui
      victoria-metrics:
        syncWave: 10
        overwriteNamespace: victoria-metrics
        ignoreDifferencesAdditional:
          - group: "apps"
            kind: StatefulSet
            jqPathExpressions:
              - .spec.volumeClaimTemplates[].apiVersion
              - .spec.volumeClaimTemplates[].kind
      fluentd:
        syncWave: 10
        overwriteNamespace: fluentd
      external-service-proxy:
        syncWave: 10
        overwriteNamespace: external-service-proxy
      gcs-files-upload:
        syncWave: 10
        overwriteNamespace: gcs-files-upload
      imageproxy:
        syncWave: 11
        overwriteNamespace: imageproxy
      memcached-admin:
        syncWave: 11
        overwriteNamespace: memcached-admin
      gcp-notify:
        syncWave: 12
      keda:
        syncWave: 13
        overwriteNamespace: keda
        ignoreDifferencesAdditional:
          # https://github.com/kedacore/keda/issues/4732
          - group: apiregistration.k8s.io
            kind: APIService
            name: v1beta1.external.metrics.k8s.io
            jsonPointers:
              - /spec/insecureSkipTLSVerify
      devops-exporter:
        syncWave: 12
      kubecost:
        syncWave: 13
        overwriteNamespace: kubecost
      elastic-ui:
        syncWave: 8
        overwriteNamespace: elastic-ui
      network-speedtest:
        syncWave: 13
        overwriteNamespace: network-speedtest
        migrationNamingEnable: true

  core:
    additional_namespaces:
      - kafka
    apps:
      hermes-be:
        syncWave: 31
        overwriteNamespace: hermes
      portal:
        syncWave: 24
        overwriteNamespace: portal
      kong-auth-router:
        syncWave: 32
        overwriteNamespace: default
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
      hermes-fe:
        syncWave: 33
        overwriteNamespace: hermes
      passport-be:
        syncWave: 34
        overwriteNamespace: passport
      bouncers:
        syncWave: 35
        overwriteNamespace: bouncers
      fms:
        syncWave: 36
        overwriteNamespace: fms
      permission-dashboard:
        syncWave: 37
        overwriteNamespace: permission-dashboard
      sms:
        syncWave: 38
        overwriteNamespace: sms
      dev-portal:
        syncWave: 39
        overwriteNamespace: dev-portal
      chatbot-be:
        syncWave: 40
        overwriteNamespace: chatbot-be

  # New structure product naming parts: pm, rsc, qrm, css, dna, sec, infra
  infra:
    notification:
      enabled: false
    additional_namespaces:
      - kube-system
      - istio-system
      - kafka
      - product-risk
      - default
      - kafka-connect-cdc
      - remote-config
      - mobileresponder
      - passport
      - defect-recommend-be
      - car
      - tracking
      - sight-be
      - qa-platform
      - factory-risk-be
      - zendesk
      - clickhouse-ds
      - dm
      - subscription
      - rs
    apps:
      cloudflare-tunnel:
        syncWave: 10
        overwriteNamespace: cloudflare-tunnel
        migrationNamingEnable: true

  sec:
    apps:
      jimmy:
        syncWave: 13
        overwriteNamespace: jimmy
        migrationNamingEnable: true

  css:
    apps:
      org-resolver:
        syncWave: 30
        overwriteNamespace: org-resolver
        migrationNamingEnable: true
      subscription-fe:
        syncWave: 30
        overwriteNamespace: subscription
        migrationNamingEnable: true
      subscription-be:
        syncWave: 30
        overwriteNamespace: subscription
        migrationNamingEnable: true

  dna: # data and analytics
    additional_namespaces:
      - kafka
    apps:
      analytics-superset:
        syncWave: 10
        overwriteNamespace: superset
        migrationNamingEnable: true
      kafka-connect-cdc:
        syncWave: 10
        overwriteNamespace: kafka-connect-cdc
        migrationNamingEnable: true
      clickhouse-ds:
        syncWave: 10
        overwriteNamespace: clickhouse-ds
        migrationNamingEnable: true
      product-risk-be:
        syncWave: 51
        overwriteNamespace: product-risk
        migrationNamingEnable: true
      pub-capa:
        syncWave: 53
        overwriteNamespace: pub-capa
        migrationNamingEnable: true
      defect-recommend-be:
        syncWave: 10
        overwriteNamespace: defect-recommend-be
        migrationNamingEnable: true
      qa-platform:
        syncWave: 50
        overwriteNamespace: qa-platform
        migrationNamingEnable: true
      factory-risk-be:
        syncWave: 51
        overwriteNamespace: factory-risk-be
        migrationNamingEnable: true
      airflow:
        syncWave: 50
        overwriteNamespace: airflow
        migrationNamingEnable: true
      dedupe-api:
        syncWave: 50
        overwriteNamespace: dedupe-api
        migrationNamingEnable: true
      timing-formula:
        syncWave: 50
        overwriteNamespace: timing-formula
        migrationNamingEnable: true
      analytic3:
        syncWave: 20
        overwriteNamespace: analytic3
        migrationNamingEnable: true
      document-validator:
        syncWave: 50
        overwriteNamespace: document-validator
        migrationNamingEnable: true

  pm:
    additional_namespaces:
      - kafka
    apps:
      tracking:
        syncWave: 90
        overwriteNamespace: tracking
        migrationNamingEnable: true

  rsc:
    additional_namespaces:
      - kafka
    apps:
      userssync:
        syncWave: 34
        overwriteNamespace: userssync
        migrationNamingEnable: true
      translation:
        syncWave: 35
        overwriteNamespace: translation
        migrationNamingEnable: true
      rs-backend:
        syncWave: 36
        overwriteNamespace: rs
        migrationNamingEnable: true
      rise-integration-api:
        syncWave: 37
        overwriteNamespace: rs
        migrationNamingEnable: true
      rs-frontend:
        syncWave: 38
        overwriteNamespace: rs
        migrationNamingEnable: true
      collabora-online:
        syncWave: 2
        migrationNamingEnable: true
        overwriteNamespace: dm
      dm-backend:
        syncWave: 20
        migrationNamingEnable: true
        overwriteNamespace: dm
        ignoreDifferencesAdditional:
          - group: "redis.redis.opstreelabs.in"
            kind: RedisReplication
            jqPathExpressions:
              - .spec.storage.volumeClaimTemplate.metadata.name
      dm-frontend:
        syncWave: 11
        migrationNamingEnable: true
        overwriteNamespace: dm
      dm-metrics:
        syncWave: 20
        migrationNamingEnable: true
        overwriteNamespace: dm
      thirdparty-reports:
        syncWave: 30
        overwriteNamespace: thirdparty-reports
        migrationNamingEnable: true

  qrm:
    additional_namespaces:
      - kafka
    apps:
      emlauncher-be:
        syncWave: 45
        overwriteNamespace: emlauncher
        migrationNamingEnable: true
      emlauncher-fe:
        syncWave: 45
        overwriteNamespace: emlauncher
        migrationNamingEnable: true
      master-data:
        syncWave: 41
        overwriteNamespace: master-data
        migrationNamingEnable: true
      mobileresponder:
        syncWave: 44
        overwriteNamespace: mobileresponder
        migrationNamingEnable: true
      remote-config:
        syncWave: 43
        overwriteNamespace: remote-config
        migrationNamingEnable: true
      zendesk-sso:
        syncWave: 45
        overwriteNamespace: zendesk
        migrationNamingEnable: true
      car:
        syncWave: 46
        overwriteNamespace: car
        migrationNamingEnable: true
      notimanager:
        syncWave: 101
        overwriteNamespace: notimanager
        migrationNamingEnable: true
      integration-api:
        syncWave: 111
        overwriteNamespace: integration-api
        migrationNamingEnable: true
      report-html:
        syncWave: 45
        overwriteNamespace: report-html
        migrationNamingEnable: true
      sight-be:
        syncWave: 71
        overwriteNamespace: sight-be
        migrationNamingEnable: true
      frontend:
        syncWave: 92
        overwriteNamespace: frontend
        migrationNamingEnable: true
      mobile-slackbot:
        syncWave: 47
        overwriteNamespace: mobile-slackbot
        migrationNamingEnable: true
