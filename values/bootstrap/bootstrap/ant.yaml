namespaceArgocd: argocd
cluster: ant #cluster to deploy argo crd's
targetCluster: ant # cluster to deploy environments
envState: ant #environment name on a targetCluster
gitlabUrl: https://gitlab.inspectorio.com
repoUrl: **************************:devops/gitops-cd.git
monoRepoUrl: &monorepo https://gitlab.inspectorio.com/saas/monorepo.git
featureDomainSuffux: .gcp-ant.inspectorio.com
featureDomainSecretName: gcp-ant-inspectorio-com
clusterBootstrapValues: ../../../../../envs/ant/values.yaml
productOrgValues:
  - "../../../../../envs/org/css.yaml"
  - "../../../../../envs/org/dna.yaml"
  - "../../../../../envs/org/infra.yaml"
  - "../../../../../envs/org/platform.yaml"
  - "../../../../../envs/org/pm.yaml"
  - "../../../../../envs/org/qrm.yaml"
  - "../../../../../envs/org/rsc.yaml"
gitlabTokenSecretname: argocd-gitlab-token
defaultDeliveryOption: manual

syncPolicy:
  automated: # automated sync by default retries failed attempts 5 times with following delays between attempts ( 5s, 10s, 20s, 40s, 80s ); retry controlled using `retry` field.
    prune: true # Specifies if resources should be pruned during auto-syncing ( false by default ).
    selfHeal: true # Specifies if partial app sync should be executed when resources are changed only in target Kubernetes cluster and no git change detected ( false by default ).
    #allowEmpty: false # Allows deleting all application resources during automatic syncing ( false by default ).
  syncOptions: # Sync options which modifies sync behavior
    - Validate=true # disables resource validation (equivalent to 'kubectl apply --validate=false') ( true by default ).
    - CreateNamespace=false # Namespace Auto-Creation ensures that namespace specified as the application destination exists in the destination cluster.
    - PrunePropagationPolicy=foreground # Supported policies are background, foreground and orphan.
    - PruneLast=true # Allow the ability for resource pruning to happen as a final, implicit wave of a sync operation
    - ServerSideApply=true
    - ApplyOutOfSyncOnly=true
  retry:
    limit: 5 # number of failed sync attempt retries; unlimited number of attempts if less than 0
    backoff:
      duration: 5s # the amount to back off. Default unit is seconds, but could also be a duration (e.g. "2m", "1h")
      factor: 2 # a factor to multiply the base duration after each failed retry
      maxDuration: 3m # the maximum amount of time allowed for the backoff strategy

previewSyncPolicy: &previewSyncPolicy
  automated: # automated sync by default retries failed attempts 5 times with following delays between attempts ( 5s, 10s, 20s, 40s, 80s ); retry controlled using `retry` field.
    prune: true # Specifies if resources should be pruned during auto-syncing ( false by default ).
    selfHeal: true # Specifies if partial app sync should be executed when resources are changed only in target Kubernetes cluster and no git change detected ( false by default ).
    allowEmpty: true # Allows deleting all application resources during automatic syncing ( false by default ).
  syncOptions: # Sync options which modifies sync behavior
    - Validate=true # disables resource validation (equivalent to 'kubectl apply --validate=false') ( true by default ).
    - CreateNamespace=false # Namespace Auto-Creation ensures that namespace specified as the application destination exists in the destination cluster.
    - PrunePropagationPolicy=foreground # Supported policies are background, foreground and orphan.
    - PruneLast=true # Allow the ability for resource pruning to happen as a final, implicit wave of a sync operation
    - ServerSideApply=true
    - ApplyOutOfSyncOnly=true
  retry:
    limit: 5 # number of failed sync attempt retries; unlimited number of attempts if less than 0
    backoff:
      duration: 5s # the amount to back off. Default unit is seconds, but could also be a duration (e.g. "2m", "1h")
      factor: 2 # a factor to multiply the base duration after each failed retry
      maxDuration: 3m # the maximum amount of time allowed for the backoff strategy

release_channels:
  dev: on_success
  rc: manual
  stable: manual

projects:
  gitlab:
    apps:
      gitlab-runner:
        syncWave: 3
        overwriteNamespace: gitlab-runner
  ant:
    apps:
      bootstrap:
        meta: true
        syncWave: 1
        overwriteNamespace: argocd
        targetClusterOverride: ant
      baseline:
        syncWave: 1
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
        overwriteNamespace: default

  devops:
    additional_namespaces:
      - kube-system
      - istio-system
      - default
    apps:
      istio:
        syncWave: 1
        syncPolicy:
          syncOptions:
            - Validate=true
            - CreateNamespace=true
            - PrunePropagationPolicy=orphan
            - PruneLast=true
            - ServerSideApply=true
          retry:
            limit: 5
            backoff:
              duration: 5s
              factor: 2
              maxDuration: 3m
        overwriteNamespace: istio-system
        disableClusterBootstrapValues: true
        ignoreDifferencesAdditional:
          - group: apps
            kind: Deployment
            jqPathExpressions:
              - .spec.template.spec.containers[].env[].valueFrom.resourceFieldRef.divisor
      certificates:
        syncWave: 1
        overwriteNamespace: cert-manager
      pomerium:
        syncWave: 2
        overwriteNamespace: pomerium
      pomerium-proxy:
        syncWave: 1
        overwriteNamespace: pomerium
      mrnotifybot:
        syncWave: 1
        overwriteNamespace: mrnotifybot
        preview:
          enabled: false
          syncPolicy: *previewSyncPolicy
          gitlabProjectId: 637 #mrnotifybot
      redis-operator:
        syncWave: 1
        overwriteNamespace: devops
      alerting:
        syncWave: 15
        overwriteNamespace: devops
      monitoring:
        syncWave: 10
        overwriteNamespace: monitoring
      pagerduty-integration-webhook:
        syncWave: 1
        overwriteNamespace: pagerduty-integration
      alerts-integration-webhook:
        syncWave: 1
        overwriteNamespace: alerts-integration-webhook
      slack-oncall:
        syncWave: 1
        overwriteNamespace: slack-oncall
      dependency-track:
        syncWave: 10
        overwriteNamespace: dependency-track
      eck-operator:
        syncWave: 1
        overwriteNamespace: eck-operator
      patch-operator:
        syncWave: 4
        overwriteNamespace: patch-operator
      eck-infra:
        syncWave: 14
        overwriteNamespace: eck-infra
      grafana:
        syncWave: 4
        overwriteNamespace: grafana
      influxdb2:
        syncWave: 6
        overwriteNamespace: perftests
      external-secrets:
        syncWave: 1
        overwriteNamespace: external-secrets
      gcsproxy:
        syncWave: 11
        overwriteNamespace: devops
      ingress-nginx:
        syncWave: 9
        overwriteNamespace: ingress-nginx
        ignoreDifferencesAdditional:
          - group: ""
            kind: Service
            namespace: ingress-nginx
            jqPathExpressions:
              - .spec.ports[].nodePort
      sonarqube:
        syncWave: 16
        overwriteNamespace: sonarqube
        ignoreDifferencesAdditional:
          - group: "apps"
            kind: StatefulSet
            jqPathExpressions:
              - .spec.volumeClaimTemplates[].apiVersion
              - .spec.volumeClaimTemplates[].kind
      victoria-metrics:
        syncWave: 10
        overwriteNamespace: victoria-metrics
        ignoreDifferencesAdditional:
          - group: "apps"
            kind: StatefulSet
            jqPathExpressions:
              - .spec.volumeClaimTemplates[].apiVersion
              - .spec.volumeClaimTemplates[].kind
      gar-cleaner:
        syncWave: 11
        overwriteNamespace: devops
      pagerduty-integration:
        syncWave: 11
        overwriteNamespace: pagerduty-integration
      mw-webhook:
        syncWave: 11
        overwriteNamespace: mw-webhook
      apicurio:
        syncWave: 50
        overwriteNamespace: apicurio
      fluentd:
        syncWave: 12
        overwriteNamespace: fluentd
      devops-exporter:
        syncWave: 12
      infrabot:
        syncWave: 13
        overwriteNamespace: infrabot
      kubecost:
        syncWave: 13
        overwriteNamespace: kubecost
      product-dev-org:
        syncWave: 14
        overwriteNamespace: product-dev-org
      tech-radar:
        syncWave: 17
        overwriteNamespace: tech-radar
      gcp-notify:
        syncWave: 12
      devops-portal-fe:
        syncWave: 15
        overwriteNamespace: devops-portal
        release_channels:
          stable: on_success
      devops-portal-be:
        syncWave: 16
        overwriteNamespace: devops-portal
        release_channels:
          stable: on_success

  infra:
    notification:
      enabled: false
    additional_namespaces:
      - kube-system
      - istio-system
      - kafka
      - product-risk
      - default
      - kafka-connect-cdc
      - remote-config
      - mobileresponder
      - passport
      - defect-recommend-be
      - car
      - tracking
      - sight-be
      - qa-platform
      - factory-risk-be
      - zendesk
      - clickhouse-ds
      - dm
      - subscription
    apps:
      cloudflare-tunnel:
        syncWave: 10
        overwriteNamespace: cloudflare-tunnel
        migrationNamingEnable: true
