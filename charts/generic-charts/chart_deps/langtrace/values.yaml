langtraceApp:
  name: langtrace
  # image: scale3labs/langtrace-client
  # langtrace_release: latest
  imagePullPolicy: IfNotPresent
  containerPort: 3000
  replicaCount: 1
  restartPolicy: OnFailure
  maxRetry: 5
  serviceType: ClusterIP
  ingress:
    create: false
    # ingressDNS: "example.com"
    # class: "nginx"
    # backendPort: 3000
    # annotations:
    #   nginx.ingress.kubernetes.io/ssl-redirect: "true"
    #   cert-manager.io/cluster-issuer: "letsencrypt-issuer"
    #   external-dns.alpha.kubernetes.io/hostname: "example.com"

postgres:
  enabled: true
  name: langtrace-postgres
  image: postgres:16.2-bookworm
  imagePullPolicy: IfNotPresent
  containerPort: 5432
  storageSize: 10Gi
  # storageClassName: gp2
  # subPath: pg-data   # Uncomment this line to use subPath

clickhouse:
  enabled: true
  name: langtrace-clickhouse
  image: clickhouse/clickhouse-server:24.5.1.1763-alpine
  imagePullPolicy: IfNotPresent
  # subPath: clickhouse-data   # Uncomment this line to use subPath
  # storageClassName: gp2
  containerPorts:
    - 8123
    - 9000
  storageSize: 10Gi

env:
  # Postgres Variables
  postgres_host: "langtrace-postgres:5432"
  postgres_user: "ltuser"
  postgres_password: "ltpasswd"
  postgres_database: "langtrace"

  # Application Variables
  NEXT_PUBLIC_APP_NAME: "Langtrace"
  NEXT_PUBLIC_ENVIRONMENT: "production"
  NEXT_PUBLIC_HOST: "http://localhost:3000"
  NEXTAUTH_SECRET: "difficultsecret"

  # Clickhouse Variables
  CLICK_HOUSE_HOST: "http://langtrace-clickhouse:8123"
  CLICK_HOUSE_USER: "lt_clickhouse_user"
  CLICK_HOUSE_PASSWORD: "clickhousepw"
  CLICK_HOUSE_DATABASE_NAME: "langtrace_traces"
  CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"

  # Admin login
  ADMIN_EMAIL: "<EMAIL>"
  ADMIN_PASSWORD: "langtraceadminpw"
  NEXT_PUBLIC_ENABLE_ADMIN_LOGIN: "true"

  # Azure AD Variables
  AZURE_AD_CLIENT_ID: ""
  AZURE_AD_CLIENT_SECRET: ""
  AZURE_AD_TENANT_ID: ""

  # additional_env_vars:

  #   # Google Auth Variables
  #   GOOGLE_CLIENT_ID: ""
  #   GOOGLE_CLIENT_SECRET: ""