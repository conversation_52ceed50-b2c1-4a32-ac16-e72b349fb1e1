apiVersion: v1
kind: Secret
metadata:
  name: langtrace-env
  labels:
    {{- include "langtrace.labels" . | nindent 4 }}
type: Opaque
data:
  POSTGRES_HOST: {{ .Values.env.postgres_host | b64enc }}
  POSTGRES_USER: {{ .Values.env.postgres_user | b64enc }}
  POSTGRES_PASSWORD: {{ .Values.env.postgres_password | b64enc }}
  POSTGRES_DATABASE: {{ .Values.env.postgres_database | b64enc }}
  POSTGRES_DB: {{ .Values.env.postgres_database | b64enc }}
  POSTGRES_URL: {{ printf "postgres://%s:%s@%s/%s" .Values.env.postgres_user .Values.env.postgres_password .Values.env.postgres_host .Values.env.postgres_database | b64enc }}
  POSTGRES_PRISMA_URL: {{ printf "postgres://%s:%s@%s/%s?pgbouncer=true&connect_timeout=15" .Values.env.postgres_user .Values.env.postgres_password .Values.env.postgres_host .Values.env.postgres_database | b64enc }}
  POSTGRES_URL_NO_SSL: {{ printf "postgres://%s:%s@%s/%s" .Values.env.postgres_user .Values.env.postgres_password .Values.env.postgres_host .Values.env.postgres_database | b64enc }}
  POSTGRES_URL_NON_POOLING: {{ printf "postgres://%s:%s@%s/%s" .Values.env.postgres_user .Values.env.postgres_password .Values.env.postgres_host .Values.env.postgres_database | b64enc }}
  NEXT_PUBLIC_APP_NAME: {{ .Values.env.NEXT_PUBLIC_APP_NAME | b64enc }}
  NEXT_PUBLIC_ENVIRONMENT: {{ .Values.env.NEXT_PUBLIC_ENVIRONMENT | b64enc }}
  NEXT_PUBLIC_HOST: {{ .Values.env.NEXT_PUBLIC_HOST | b64enc }}
  NEXTAUTH_SECRET: {{ .Values.env.NEXTAUTH_SECRET | b64enc }}
  NEXTAUTH_URL: {{ .Values.env.NEXT_PUBLIC_HOST | b64enc }}
  NEXTAUTH_URL_INTERNAL: {{ .Values.env.NEXT_PUBLIC_HOST | b64enc }}
  CLICK_HOUSE_HOST: {{ .Values.env.CLICK_HOUSE_HOST | b64enc }}
  CLICK_HOUSE_USER: {{ .Values.env.CLICK_HOUSE_USER | b64enc }}
  CLICK_HOUSE_PASSWORD: {{ .Values.env.CLICK_HOUSE_PASSWORD | b64enc }}
  CLICK_HOUSE_DATABASE_NAME: {{ .Values.env.CLICK_HOUSE_DATABASE_NAME | b64enc }}
  ADMIN_EMAIL: {{ .Values.env.ADMIN_EMAIL | b64enc }}
  ADMIN_PASSWORD: {{ .Values.env.ADMIN_PASSWORD | b64enc }}
  NEXT_PUBLIC_ENABLE_ADMIN_LOGIN: {{ .Values.env.NEXT_PUBLIC_ENABLE_ADMIN_LOGIN | b64enc }}
  CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: {{ .Values.env.CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT | b64enc }}
  CLICKHOUSE_PASSWORD: {{ .Values.env.CLICK_HOUSE_PASSWORD | b64enc }}
  CLICKHOUSE_USER: {{ .Values.env.CLICK_HOUSE_USER | b64enc }}
  CLICKHOUSE_DB: {{ .Values.env.CLICK_HOUSE_DATABASE_NAME | b64enc }}
  AZURE_AD_CLIENT_ID: {{ .Values.env.AZURE_AD_CLIENT_ID | b64enc }}
  AZURE_AD_CLIENT_SECRET: {{ .Values.env.AZURE_AD_CLIENT_SECRET | b64enc }}
  AZURE_AD_TENANT_ID: {{ .Values.env.AZURE_AD_TENANT_ID | b64enc }}
{{- range $key, $value := .Values.env.additional_env_vars }}
  {{ $key }}: {{ $value | b64enc }}
{{- end }}