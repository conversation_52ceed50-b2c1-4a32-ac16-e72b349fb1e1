{{- if .Values.clickhouse.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Values.clickhouse.name }}-pvc
  labels:
    {{- include "langtrace.labels" . | nindent 4 }}
spec:
  {{- if .Values.clickhouse.storageClassName }}
  storageClassName: {{ .Values.clickhouse.storageClassName }}
  {{- end }}
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ .Values.clickhouse.storageSize }}
{{- end }}
