{{- if .Values.postgres.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Values.postgres.name }}-pvc
  labels:
    {{- include "langtrace.labels" . | nindent 4 }}
spec:
  {{- if .Values.postgres.storageClassName }}
  storageClassName: {{ .Values.postgres.storageClassName }}
  {{- end }}
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ .Values.postgres.storageSize }}
{{- end}}
