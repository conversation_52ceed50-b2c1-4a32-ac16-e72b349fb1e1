langtrace:
  langtraceApp:
    name: langtrace
    # image: scale3labs/langtrace-client
    # langtrace_release: latest
    imagePullPolicy: IfNotPresent
    containerPort: 3000
    replicaCount: 1
    restartPolicy: OnFailure
    maxRetry: 5
    serviceType: ClusterIP
    ingress:
      create: false
      # ingressDNS: "example.com"
      # class: "nginx"
      # backendPort: 3000
      # annotations:
      #   nginx.ingress.kubernetes.io/ssl-redirect: "true"
      #   cert-manager.io/cluster-issuer: "letsencrypt-issuer"
      #   external-dns.alpha.kubernetes.io/hostname: "example.com"

  postgres:
    enabled: true
    name: langtrace-postgres
    image: postgres:16.2-bookworm
    imagePullPolicy: IfNotPresent
    containerPort: 5432
    storageSize: 10Gi

  clickhouse:
    enabled: true
    name: langtrace-clickhouse
    image: clickhouse/clickhouse-server:24.5.1.1763-alpine
    imagePullPolicy: IfNotPresent
    containerPorts:
      - 8123
      - 9000
    storageSize: 10Gi

  env:
  # Postgres Variables
  postgres_host: "langtrace-postgres:5432"

  # Application Variables
  NEXT_PUBLIC_HOST: "http://localhost:3000"
  NEXTAUTH_SECRET: "difficultsecret"

  # Clickhouse Variables
  CLICK_HOUSE_HOST: "http://langtrace-clickhouse:8123"
  CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"


langtrace-common: {}

istio-ingress: {}
