clickhouse-ds:
  infra:
    services:
      clickhousecluster:
        main:
          clickhouse:
            image: asia-docker.pkg.dev/inspectorio-ant/mirror/clickhouse/altinity/clickhouse-server:24.8.11.51285.altinitystable
            shards: 1
            replicas: 2
            secure: "no"
            insecure: "yes"
            resources:
              requests:
                memory: "6Gi"
                cpu: "0.5"
              limits:
                memory: "6Gi"
                cpu: "2"
            settings:
              keep_alive_timeout: 120
            udf:
              enabled: false
            metrics:
              enabled: true
            backups:
              enabled: false
              image: "asia-docker.pkg.dev/inspectorio-ant/mirror/clickhouse/altinity/clickhouse-backup:2.6.5"
            persistency:
              storage: 5Gi

          clickhousekeeper:
            enabled: true
            image: asia-docker.pkg.dev/inspectorio-ant/mirror/clickhouse/clickhouse/clickhouse-keeper:25.1.3.23-alpine
            resources:
              requests:
                memory: "256Mi"
                cpu: "0.5"
              limits:
                memory: "256Mi"
                cpu: "1"
            persistency:
              storage: 2Gi
