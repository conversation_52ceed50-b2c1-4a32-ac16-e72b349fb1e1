{{- if and (hasKey .Values "pomerium-postgresql") (has<PERSON>ey (index .Values "pomerium-postgresql") "enabled") (eq (index (index .Values "pomerium-postgresql") "enabled") true) }}
apiVersion: acid.zalan.do/v1
kind: postgresql
metadata:
  name: {{ index (index .Values "pomerium-postgresql") "name" }}
  namespace: {{ index (index .Values "pomerium-postgresql") "namespace" }}
spec:
  teamId: {{ index (index .Values "pomerium-postgresql") "teamId" }}
  numberOfInstances: {{ index (index .Values "pomerium-postgresql") "instances" }}
  postgresql:
    version: "{{ index (index .Values "pomerium-postgresql") "version" }}"
    parameters:
      shared_buffers: "{{ index (index (index .Values "pomerium-postgresql") "parameters") "shared_buffers" }}"
      max_connections: "{{ index (index (index .Values "pomerium-postgresql") "parameters") "max_connections" }}"
      log_statement: "{{ index (index (index .Values "pomerium-postgresql") "parameters") "log_statement" }}"
  volume:
    size: {{ index (index (index .Values "pomerium-postgresql") "volume") "size" }}
  users:
    pomerium:
      {{- toYaml (index (index (index .Values "pomerium-postgresql") "users") "pomerium") | nindent 6 }}
  databases:
    {{ index (index .Values "pomerium-postgresql") "database" }}: pomerium
  resources:
    requests:
      cpu: {{ index (index (index (index .Values "pomerium-postgresql") "resources") "requests") "cpu" }}
      memory: {{ index (index (index (index .Values "pomerium-postgresql") "resources") "requests") "memory" }}
    limits:
      cpu: {{ index (index (index (index .Values "pomerium-postgresql") "resources") "limits") "cpu" }}
      memory: {{ index (index (index (index .Values "pomerium-postgresql") "resources") "limits") "memory" }}
{{- end }}