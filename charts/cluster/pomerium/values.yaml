pomerium-common:
  externalSecretStore:
    pomerium-shared: # name of k8s secret
      source:
        data:
          - secretKey: COOKIE_SECRET # name of key in k8s secret
            remoteRef:
              key: pomerium-cookie_secret
          - secretKey: SHARED_SECRET # name of key in k8s secret
            remoteRef:
              key: pomerium-shared_secret
          - secretKey: IDP_CLIENT_ID # name of key in k8s secret
            remoteRef:
              key: pomerium-idp_client_id
          - secretKey: IDP_CLIENT_SECRET # name of key in k8s secret
            remoteRef:
              key: pomerium-idp_service_client
          - secretKey: IDP_SERVICE_ACCOUNT # name of key in k8s secret
            remoteRef:
              key: pomerium-idp_service_account

  environmentFrom:
    - secretRef:
        name: pomerium-shared

  serviceAccount:
    create: false
    name: pomerium
    enableWorkloadIdentity: true

pomerium:
  image:
    repository: 'asia-docker.pkg.dev/inspectorio-ant/mirror/pomerium/pomerium/pomerium'
    tag: 'v0.29.0'

  # For detailed explanation of each of the configuration settings see
  # https://www.pomerium.io/reference/
  priorityClassName: "infra-high-priority"

  config:
    # routes under this wildcard domain are handled by pomerium
    cookie_expire: 12h
    generateTLS: false
    generateTLSAnnotations: {}
    forceGenerateTLS: false
    generateSigningKey: true
    forceGenerateSigningKey: false
    forceGenerateServiceSecrets: false
    existingSharedSecret: "pomerium-shared"
    existingPolicy: ""
    insecure: true
    insecureProxy: true
    extraOpts:
      timeout_read: 300s
      timeout_write: 300s

  authenticate:
    idp:
      provider: "azure"
      clientID: "CHECK_ENV_VARS_FOR_CLIENT_ID"
      clientSecret: "CHECK_ENV_VARS_FOR_CLIENT_SECRET"
      url: "https://login.microsoftonline.com/c483e930-d691-49ab-98df-e6c3b83b633b/v2.0"
    autoscaling:
      enabled: false
      minReplicas: 1
      maxReplicas: 5
      targetCPUUtilizationPercentage: 50
      targetMemoryUtilizationPercentage: 50
    pdb:
      enabled: false
      minAvailable: 1
    serviceAccount:
      nameOverride: "pomerium"

  authorize:
    autoscaling:
      enabled: false
      minReplicas: 1
      maxReplicas: 5
      targetCPUUtilizationPercentage: 50
      targetMemoryUtilizationPercentage: 50
    deployment:
      extraEnv:
        JWT_CLAIMS_HEADERS: email
    pdb:
      enabled: false
      minAvailable: 1

  databroker:
    replicaCount: 1

  proxy:
    autoscaling:
      enabled: false
      minReplicas: 2
      maxReplicas: 5
      targetCPUUtilizationPercentage: 50
      targetMemoryUtilizationPercentage: 50
    pdb:
      enabled: false
      minAvailable: 1

  ingress:
    enabled: false

  resources:
    {}

  replicaCount: 1

  affinity:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 1
          preference:
            matchExpressions:
              - key: infra
                operator: In
                values:
                  - "true"
  tolerations:
    - effect: NoSchedule
      key: pool-type
      operator: Equal
      value: "infra"

  serviceMonitor:
    enabled: true
    namespace: "pomerium"
    labels:
      prometheus: "prometheus-main"

  metrics:
    enabled: true

  podLabels:
    app.inspectorio.com/service: pomerium
    app.inspectorio.com/criticality: P3
    app.inspectorio.com/maintainer: sre
    app.inspectorio.com/owner: sre
    app.inspectorio.com/product: infra

istio-ingress: {}

pomerium-postgresql:
  enabled: true
  name: pomerium-db
  namespace: postgres-operator
  teamId: acid
  instances: 1
  version: "15"
  parameters:
    shared_buffers: "128MB"
    max_connections: "200"
    log_statement: "all"
  volume:
    size: 10Gi
  users:
    pomerium:
      - superuser
      - createdb
  database: pomeriumdb
  resources:
    requests:
      cpu: 200m
      memory: 200Mi
    limits:
      cpu: 1000m
      memory: 1Gi
