admin-gui:
  enabled: true
  nodeSelector:
    infra: "true"
  tolerations:
    - key: "pool-type"
      operator: "Equal"
      value: "infra"
      effect: "NoSchedule"

  config:
    node_env: production
    no_auth: "true"
    konga_node_seeds:
      - name: kong-ingress-api
        type: default
        health_check: false
        kong_admin_url: "http://kong-gateway-ingress-api-admin:8001"
      - name: kong-ingress-integration
        type: default
        health_check: false
        kong_admin_url: "http://kong-gateway-ingress-integration-cp-admin:8001"

  istio:
    enabled: true

plugins:
  defaultIngressClassName: kong-ingress-api
  enable: true
  clusterPlugins:
    cors-default:
      global: true
      plugin: cors
      ingressClass:
        - kong-ingress-api
        - kong-ingress-integration
      config:
        origins:
          - "*"
        methods:
          - "GET"
          - "HEAD"
          - "PUT"
          - "PATCH"
          - "POST"
          - "DELETE"
          - "OPTIONS"
        credentials: false
        preflight_continue: false
        headers:
          - "User-Agent"
          - "X-Requested-With"
          - "If-Modified-Since"
          - "Cache-Control"
          - "Content-Type"
          - "Range"
          - "authorization"
          - "auth"
          - "Saas-Backend"
          - "x-client"
          - "rs-auth"
          - "service"
          - "token"
          - "apikey"
        exposed_headers:
          - "Cache-Control"
          - "Content-Language"
          - "Content-Type"
          - "Expires"
          - "Last-Modified"
          - "Pragma"
          - "location"
    prometheus:
      global: true
      plugin: prometheus
      ingressClass:
        - kong-ingress-api
        - kong-ingress-integration
      config:
        status_code_metrics: true
        latency_metrics: true
        bandwidth_metrics: true
    response-transformer-global:
      global: true
      plugin: response-transformer
      ingressClass:
        - kong-ingress-api
        - kong-ingress-integration
      config:
        remove:
          headers:
            #This header contains istio service names
            #we need to remove in order not expose
            #information about our internal infrastructure
            - "x-envoy-decorator-operation"
    stdout-log:
      global: true
      plugin: stdout-log
      ingressClass:
        - kong-ingress-api
        - kong-ingress-integration
      config:
        token_header_names: ["auth", "authorization"]
    bouncer-auth-default:
      global: false
      ingressClass:
        - kong-ingress-api
      plugin: bouncer-auth
      config:
        bouncer_url: "http://bouncers-main.bouncers.svc.cluster.local/bouncers/v1/authorize"
    bouncer-auth-query-string:
      global: false
      plugin: bouncer-auth
      ingressClass:
        - kong-ingress-api
      config:
        bouncer_url: "http://bouncers-main.bouncers.svc.cluster.local/bouncers/v1/authorize"
        option: query_string
    bouncer-auth-disabled:
      global: false
      plugin: bouncer-auth
      ingressClass:
        - kong-ingress-api
      config:
        bouncer_url: "http://bouncers-main.bouncers.svc.cluster.local/bouncers/v1/authorize"
        use_bouncer: false
    fms-auth:
      plugin: fms-auth
      global: false
      ingressClass:
        - kong-ingress-api
      config:
        token_cheker_url: "http://bouncers-main.bouncers.svc.cluster.local/bouncers/v1/authorize"
    # ddtrace:
    #   plugin: ddtrace
    #   global: false
    #   ingressClass:
    #     - kong-ingress-api
    #   config:
    #     injection_propagation_styles:
    #       - datadog
    #       - tracecontext
    #     extraction_propagation_styles:
    #       - datadog
    #       - tracecontext
    #     initial_sample_rate: 0.5
    #     initial_samples_per_second: 200

  plugins: {}

alerting-kong:
  alertrules:
    prometheusName: prometheus-main
    interval: 1m
    rules:
      absent-kong-metrics:
        runbook_url: ""
        description: Check all Kong processes are up and running
        summary: Absent of kong_http_requests_total metric
        severity:
          warning:
            expr: absent(kong_http_requests_total)
            for: 5m
          error:
            expr: absent(kong_http_requests_total)
            for: 10m
          critical:
            expr: absent(kong_http_requests_total)
            for: 30m
