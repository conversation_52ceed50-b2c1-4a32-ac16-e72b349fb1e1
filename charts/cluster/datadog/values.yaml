datadog-common:
  externalSecretStore:
    datadog: # name of k8s secret
      source:
        data:
          - secretKey: api-key # name of key in k8s secret
            remoteRef:
              key: datadog-api-key
          - secretKey: token # name of key in k8s secret
            remoteRef:
              key: datadog-token

  environmentFrom:
    - secretRef:
        name: datadog

  serviceAccount:
    create: false
    name: datadog
    enableWorkloadIdentity: true

  alertrules:
    prometheusName: prometheus-main
    interval: 1m
    rules:
      dd-agent-crashloopback:
        runbook_url: https://inspectoriodocs.atlassian.net/wiki/x/Y4AZ3Q
        summary: 'Datadog agent pod {{ $value }} is under CrashLoopBackOff'
        severity:
          error:
            expr: kube_pod_container_status_waiting_reason{pod=~"datadog-.*", reason="CrashLoopBackOff"}
            for: 5m
            labels:
              group: datadog
      dd-agent-ds-low-ready-pods:
        runbook_url: "https://inspectoriodocs.atlassian.net/wiki/x/lwAT3Q"
        summary: 'Datadog agent daemonset total ready status is not match total saas/spot nodes'
        severity:
          error:
            expr: (count(kube_node_info{node!~".*-infra-pool-.*"})) - (count(kube_pod_status_ready{condition="true", pod=~"datadog-.*", pod!~"datadog-cluster-agent.*", pod!~"datadog-gateway.*"})) != 0
            for: 10m
            labels:
              group: datadog

datadog:
  targetSystem: "linux"
  commonLabels:
    app.inspectorio.com/service: datadog
    app.inspectorio.com/criticality: P2
    app.inspectorio.com/maintainer: sre
    app.inspectorio.com/owner: sre
    app.inspectorio.com/product: infra

  datadog:
    apiKeyExistingSecret: "datadog"
    apm:
      ## @param enabled - boolean - optional - default: false
      ## Enable this to enable APM and tracing, on port 8126
      #
      enabled: true
    logs:
      ## @param enabled - boolean - optional - default: false
      ## Enables this to activate Datadog Agent log collection.
      #
      enabled: true

      ## @param containerCollectAll - boolean - optional - default: false
      ## Enable this to allow log collection for all containers.
      #
      containerCollectAll: false

    kubeStateMetricsCore: 
      # datadog.kubeStateMetricsCore.enabled -- Enable the kubernetes_state_core check in the Cluster Agent (Requires Cluster Agent 1.12.0+)
      ## ref: https://docs.datadoghq.com/integrations/kubernetes_state_core
      enabled: false

    remoteConfiguration:
      # datadog.remoteConfiguration.enabled -- Set to true to enable remote configuration.
      # Consider using remoteConfiguration.enabled instead
      enabled: false

    env:
      - name: DD_APM_MAX_TPS
        value: 2
      - name: DD_CONTAINER_EXCLUDE
        value: name:.*
      - name: DD_CONTAINER_INCLUDE
        value: >
          name:sms.* name:hermes.* name:passport-be.*
      - name: DD_ENABLE_PAYLOADS_EVENTS
        value: "false"
      - name: DD_ENABLE_PAYLOADS_SERIES
        value: "false"
      - name: DD_ENABLE_PAYLOADS_SERVICE_CHECKS
        value: "false"
      - name: DD_ENABLE_PAYLOADS_SKETCHES
        value: "false"

  agents:
    useHostNetwork: true
    tolerations:
      - effect: NoSchedule
        key: pool-type
        operator: Equal
        value: "spot"
      - key: "pool-type"
        operator: "Equal"
        value: "data-platform"
        effect: "NoSchedule"

    containers:
      traceAgent:
        resources:
          requests:
            cpu: 200m

  clusterAgent:
    replicas: 3
    tokenExistingSecret: "datadog"
    enabled: true
    tolerations:
      - effect: NoSchedule
        key: pool-type
        operator: Equal
        value: "infra"
    nodeSelector:
      infra: "true"
    admissionController:
      enabled: false
