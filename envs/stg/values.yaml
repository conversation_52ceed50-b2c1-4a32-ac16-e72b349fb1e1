global:
  roles:
    default:
      worker:
        nodeSelector:
          spot: "true"
        tolerations:
          - key: "pool-type"
            operator: "Equal"
            value: "spot"
            effect: "NoSchedule"
      cronjob:
        nodeSelector:
          spot: "true"
        tolerations:
          - key: "pool-type"
            operator: "Equal"
            value: "spot"
            effect: "NoSchedule"
      job:
        nodeSelector:
          spot: "true"
        tolerations:
          - key: "pool-type"
            operator: "Equal"
            value: "spot"
            effect: "NoSchedule"
      eck:
        nodeSelector:
          infra: "true"
        tolerations:
          - key: "pool-type"
            operator: "Equal"
            value: "infra"
            effect: "NoSchedule"
      redis:
        nodeSelector:
          infra: "true"
        tolerations:
          - key: "pool-type"
            operator: "Equal"
            value: "infra"
            effect: "NoSchedule"
        affinity: {}
  infra:
    settings:
      defaultKongIngressClassName: kong-ingress-api
      defaultKongTlsSecretName: inspectorio-com-common-tls
      probes:
        interval: 30s
      kongIngress:
        api:
          tlsSecretName: inspectorio-com-common-tls
          className: kong-ingress-api
          hosts:
            - domains:
                - "api.stg.inspectorio.com"
              tlsSecretName: inspectorio-com-common-tls
        integration:
          tlsSecretName: inspectorio-com-common-tls
          className: kong-ingress-integration
          hosts:
            - domains:
                - "integration.stg.inspectorio.com"
              tlsSecretName: inspectorio-com-common-tls
      prometheusOperatorName: prometheus-main
      prometheusMainAddress: http://monitoring-main-prometheus.monitoring.svc.cluster.local:9090
      pushGatewayAddress: http://monitoring-pushgw.monitoring.svc.cluster.local
      datadog_enabled: True
      defaultCertificateIssuer: letsencrypt
      gcp_project: inspectorio-saas-1514348758561
      cloud:
        provider: gcp
        region: us-west1
        project: inspectorio-saas-1514348758561
      gke:
        region: us-west1
        cluster_name: inspectorio-staging
      alertRuleDefaultInterval: 60s
      blackbox_exporters:
        http: monitoring-prometheus-blackbox-exporter.monitoring.svc.cluster.local:9115
        tcp: monitoring-prometheus-blackbox-exporter.monitoring.svc.cluster.local:9115
        domain: monitoring-domain-exporter.monitoring.svc.cluster.local
      alertmanager:
        secret: tf-generic-secret-alertmanager
        slackSecretKey: SLACK_API_URL
        pagerDutyServiceKey: PAGERDUTY_SERVICE_KEY
        severity:
          default:
            slackChannel: "#subscription-sync-to-rise-prod"
            pagerDutyServiceKey: "PAGERDUTY_SERVICE_KEY"
          critical:
            slackChannel: "#subscription-sync-to-rise-prod-critical"
            pagerDutyServiceKey: "PAGERDUTY_SERVICE_KEY_MEDIUM"
        groupWait: 30s
        groupInterval: 5m
        repeatInterval: 15m
      networkPolicy:
        defaultIngress:
          - from:
              - namespaceSelector:
                  matchExpressions:
                    - key: name
                      operator: In
                      values:
                        [
                          "kong",
                          "istio-system",
                          "pomerium",
                          "monitoring",
                          "kube-system",
                          "datadog",
                        ]
        defaultEgress:
          - to:
              - namespaceSelector:
                  matchLabels:
                    kubernetes.io/metadata.name: kube-system
                podSelector:
                  matchLabels:
                    k8s-app: kube-dns
            ports:
              - port: 53
                protocol: UDP
              - port: 53
                protocol: TCP
          - to:
              - ipBlock:
                  cidr: 0.0.0.0/0
                  except:
                    - 10.0.0.0/8
                    - **********/12
            ports:
              - protocol: TCP
                port: 80
              - protocol: TCP
                port: 443
          - to:
              - namespaceSelector:
                  matchExpressions:
                    - key: name
                      operator: In
                      values:
                        - kong
                        - istio-system
                        - monitoring
                        - kube-system
                        - datadog
          - to:
              - ipBlock:
                  cidr: ***************/32 # https://cloud.google.com/kubernetes-engine/docs/how-to/network-policy#network-policy-and-workload-identity
            ports:
              - protocol: TCP
                port: 80
              - protocol: TCP
                port: 443
          - to:
              - ipBlock:
                  cidr: ***************/32 # https://cloud.google.com/kubernetes-engine/docs/how-to/network-policy#network-policy-and-workload-identity
            ports:
              - protocol: TCP
                port: 988
          - to:
              - ipBlock:
                  cidr: 127.0.0.1/32 # https://cloud.google.com/kubernetes-engine/docs/how-to/network-policy#network-policy-and-workload-identity
            ports:
              - protocol: TCP
                port: 988
          - ports:
              - port: 8126
                protocol: TCP
              - port: 8126
                protocol: UDP
            to:
              - ipBlock:
                  cidr: 10.0.0.0/8 # datadog-agent

    eck:
      nodeSelector:
        infra: "true"
      tolerations:
        - key: "pool-type"
          operator: "Equal"
          value: "infra"
          effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app.kubernetes.io/name
                    operator: In
                    values:
                      - eck
              topologyKey: "kubernetes.io/hostname"
    pgbouncer:
      exporter:
        image: "asia-docker.pkg.dev/inspectorio-ant/mirror/pgbouncer/prometheuscommunity/pgbouncer-exporter:v0.10.2"
    istio:
      gateways:
        service: istio-system/istio-ingress-service
        common: istio-system/istio-ingress-common
        assets: istio-system/istio-ingress-assets
      gatewayLabels:
        service:
          istio: ingress-service
        common:
          istio: ingress-common
        assets:
          istio: ingress-assets
      corsPolicy:
        allowOrigins:
          - exact: "*"
        allowMethods:
          - GET
          - HEAD
          - PUT
          - PATCH
          - POST
          - DELETE
          - OPTIONS
        allowHeaders:
          - User-Agent
          - X-Requested-With
          - If-Modified-Since
          - Cache-Control
          - Content-Type
          - Range
          - auth
          - authorization
          - Saas-Backend
          - x-client
          - rs-auth
          - Pragma
          - service
          - apikey # Allow docs.inspectorio.com/api send request through browser
        exposeHeaders:
          - Cache-Control
          - Content-Language
          - Content-Type
          - Expires
          - Last-Modified
          - Pragma
          - location
        allowCredentials: false
        maxAge: "24h"

  meta_settings:
    env_name: stg
    maintainer: sre

namespace:
  config:
    argocd: {}
    cert-manager: {}
    clickhouse-ds: {}
    clickhouse-operator: {}
    default:
      labels:
        istio-injection: enabled
    devops: {}
    devops-portal: {}
    monitoring: {}
    dm:
      labels:
        istio-injection: enabled
    ds: {}
    eck-operator: {}
    eck-sight: {}
    eck-integration: {}
    eck-infra: {}
    eck-datasync: {}
    external-secrets: {}
    gitlab-runner: {}
    gcs-files-upload: {}
    istio-system: {}
    kafka: {}
    sftpgo: {}
    kafka-connect-cdc: {}
    kong:
      labels:
        istio-injection: enabled
    kube-node-lease: {}
    kube-public: {}
    kube-system: {}
    postgres-operator: {}
    postgres-client: {}
    pgbouncer: {}
    redis: {}
    redis-ha: {}
    redis-integration-api: {}
    redis-operator: {}
    spark-hs: {}
    pomerium: {}
    keda: {}
    kubecost: {}
    superset:
      labels:
        istio-injection: enabled
    product-risk:
      labels:
        istio-injection: enabled
    kafka-operator: {}
    ingress-nginx:
      labels:
        istio-injection: enabled
    datadog: {}
    thirdparty-reports: {}
    kafka-ui: {}
    kafka-schema-registry: {}
    hermes:
      labels:
        istio-injection: enabled
    portal:
      labels:
        istio-injection: enabled
    patch-operator: {}
    pub-capa: {}
    redis-ui: {}
    elastic-ui: {}
    devpi-proxy: {}
    victoria-metrics: {}
    fluentd: {}
    master-data:
      labels:
        istio-injection: enabled
    remote-config:
      labels:
        istio-injection: enabled
    sight-be:
      labels:
        istio-injection: enabled
    userssync: {}
    mobileresponder:
      labels:
        istio-injection: enabled
    passport:
      labels:
        istio-injection: enabled
    defect-recommend-be:
      labels:
        istio-injection: enabled
    tracking:
      labels:
        istio-injection: enabled
    car:
      labels:
        istio-injection: enabled
    airflow:
      labels:
        istio-injection: enabled
    imageproxy:
      labels:
        istio-injection: enabled
    translation:
      labels:
        istio-injection: enabled
    report-html:
      labels:
        istio-injection: enabled
    frontend:
      labels:
        istio-injection: enabled
    memcached-admin:
      labels:
        istio-injection: enabled
    qa-platform:
      labels:
        istio-injection: enabled
    bouncers:
      labels:
        istio-injection: enabled
    factory-risk-be:
      labels:
        istio-injection: enabled
    emlauncher:
      labels:
        istio-injection: enabled
    timing-formula:
      labels:
        istio-injection: enabled
    notimanager:
      labels:
        istio-injection: enabled
    fms:
      labels:
        istio-injection: enabled
    dedupe-api:
      labels:
        istio-injection: enabled
    integration-api:
      labels:
        istio-injection: enabled
    permission-dashboard:
      labels:
        istio-injection: enabled
    sms:
      labels:
        istio-injection: enabled
    rs:
      labels:
        istio-injection: enabled
    zendesk:
      labels:
        istio-injection: enabled
    analytic3:
      labels:
        istio-injection: enabled
    h1way:
      labels:
        istio-injection: enabled
    document-validator:
      labels:
        istio-injection: enabled
    chatbot-be:
      labels:
        istio-injection: enabled
    sample-service:
      labels:
        istio-injection: enabled
    pagerduty-integration: {}
    alerts-integration-webhook: {}
    dev-portal:
      labels:
        istio-injection: enabled
    org-resolver:
      labels:
        istio-injection: enabled
    subscription:
      labels:
        istio-injection: enabled
    mobile-slackbot:
      labels:
        istio-injection: enabled
    gatekeeper: {}
    mrnotifybot: {}
    jimmy:
      labels:
        istio-injection: enabled
    gitlab-dr: {}
    infrabot: {}
    cloudflare-tunnel: {}
    ins-design-tokens: {}
    network-speedtest: {}
    langtrace: {}

datadog:
  agents:
    rbac:
      serviceAccountAnnotations:
        iam.gke.io/gcp-service-account: <EMAIL>

pomerium:
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>

external-secrets:
  serviceAccount:
    annotations:
      iam.gke.io/gcp-service-account: <EMAIL>
